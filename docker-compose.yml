version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: samsara-postgres
    environment:
      POSTGRES_USER: samsara
      POSTGRES_PASSWORD: samsara_dev_pass
      POSTGRES_DB: samsara_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U samsara"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - samsara-network

  # Weaviate Vector Database
  weaviate:
    image: semitechnologies/weaviate:1.23.7
    container_name: samsara-weaviate
    ports:
      - "8080:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: ''
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:8080/v1/.well-known/ready"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - samsara-network

  # Redis for Celery broker and caching
  redis:
    image: redis:7-alpine
    container_name: samsara-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - samsara-network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: samsara-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***************************************************/samsara_db
      - REDIS_URL=redis://redis:6379/0
      - WEAVIATE_URL=http://weaviate:8080
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
    env_file:
      - .env
    volumes:
      - ./backend:/app
      - ./aws:/app/aws
      - /tmp:/tmp
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      weaviate:
        condition: service_healthy
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - samsara-network

  # Celery Worker - Consolidated (20 workers, all queues)
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: samsara-celery-worker
    environment:
      - DATABASE_URL=***************************************************/samsara_db
      - REDIS_URL=redis://redis:6379/0
      - WEAVIATE_URL=http://weaviate:8080
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
    env_file:
      - .env
    volumes:
      - ./backend:/app
      - ./aws:/app/aws
      - /tmp:/tmp
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      weaviate:
        condition: service_healthy
      backend:
        condition: service_started
    command: celery -A app.core.celery_app worker --loglevel=info --concurrency=20 --max-tasks-per-child=20 --hostname=worker@%h
    networks:
      - samsara-network

  # Celery Beat (Scheduler)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: samsara-celery-beat
    environment:
      - DATABASE_URL=***************************************************/samsara_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
    env_file:
      - .env
    volumes:
      - ./backend:/app
      - ./aws:/app/aws
    depends_on:
      - redis
    command: celery -A app.core.celery_app beat --loglevel=info
    networks:
      - samsara-network

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: samsara-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    command: npm start
    networks:
      - samsara-network

volumes:
  postgres_data:
  weaviate_data:
  redis_data:

networks:
  samsara-network:
    driver: bridge
