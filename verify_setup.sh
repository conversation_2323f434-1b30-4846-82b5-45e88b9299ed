#!/bin/bash

# Quick verification script to check if the migration fix is working
# Run this after starting containers to verify everything is working

set -e

echo "🔍 Verifying Samsara Setup..."
echo "============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if containers are running
echo "📦 Checking container status..."
if docker compose ps | grep -q "Up"; then
    print_status "Containers are running"
else
    print_error "Containers are not running. Run 'docker compose up -d' first"
    exit 1
fi

# Check backend health
echo "🏥 Checking backend health..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    print_status "Backend is healthy"
else
    print_error "Backend is not responding"
    echo "Check backend logs: docker compose logs backend"
    exit 1
fi

# Check database connection
echo "🗄️  Checking database..."
if docker compose exec -T postgres psql -U samsara -d samsara_db -c "SELECT 1;" > /dev/null 2>&1; then
    print_status "Database is accessible"
else
    print_error "Cannot connect to database"
    exit 1
fi

# Check if videos table exists
echo "📋 Checking database schema..."
if docker compose exec -T postgres psql -U samsara -d samsara_db -c "\d videos" > /dev/null 2>&1; then
    print_status "Videos table exists"
else
    print_error "Videos table does not exist - migration issue"
    exit 1
fi

# Check migration status
echo "🔄 Checking migration status..."
current_version=$(docker compose exec -T postgres psql -U samsara -d samsara_db -t -c "SELECT version_num FROM alembic_version;" 2>/dev/null | tr -d ' \n' || echo "")
if [ -n "$current_version" ]; then
    print_status "Migration version: $current_version"
else
    print_warning "No migration version found (this might be normal for fresh setup)"
fi

# Check if frontend is accessible
echo "🌐 Checking frontend..."
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    print_status "Frontend is accessible"
else
    print_warning "Frontend is not responding (may still be starting)"
fi

echo ""
echo "🎉 Verification Summary:"
echo "======================="
print_status "All core services are working correctly"
print_status "Database schema is properly initialized"
print_status "The migration fix is working as expected"

echo ""
echo "🔗 Service URLs:"
echo "- Backend API: http://localhost:8000"
echo "- API Documentation: http://localhost:8000/docs"
echo "- Frontend: http://localhost:3000"
echo "- Database: localhost:5432"

echo ""
echo "📝 Next steps:"
echo "- Upload a video to test the full functionality"
echo "- Check logs if you encounter any issues: docker compose logs [service-name]"
