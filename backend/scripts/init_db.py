#!/usr/bin/env python3
"""
Database initialization script that runs Alembic migrations.
This ensures the database schema is properly set up before the application starts.
"""

import os
import sys
import time
import subprocess
from pathlib import Path
from loguru import logger
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings


def wait_for_database(max_retries=30, retry_interval=2):
    """Wait for database to be available."""
    logger.info("Waiting for database to be available...")
    
    for attempt in range(max_retries):
        try:
            engine = create_engine(settings.DATABASE_URL)
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("✅ Database is available!")
            return True
        except OperationalError as e:
            logger.warning(f"Database not ready (attempt {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
            else:
                logger.error("❌ Database failed to become available")
                return False
    
    return False


def run_migrations():
    """Run Alembic migrations."""
    logger.info("Running Alembic migrations...")
    
    try:
        # Change to backend directory for alembic
        os.chdir(backend_dir)
        
        # Run alembic upgrade head
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            capture_output=True,
            text=True,
            check=True
        )
        
        logger.info("✅ Migrations completed successfully!")
        logger.debug(f"Migration output: {result.stdout}")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Migration failed: {e}")
        logger.error(f"Migration stderr: {e.stderr}")
        logger.error(f"Migration stdout: {e.stdout}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error during migration: {e}")
        return False


def check_migration_status():
    """Check current migration status."""
    logger.info("Checking migration status...")
    
    try:
        os.chdir(backend_dir)
        
        # Check current migration status
        result = subprocess.run(
            ["alembic", "current"],
            capture_output=True,
            text=True,
            check=True
        )
        
        logger.info(f"Current migration status: {result.stdout.strip()}")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.warning(f"Could not check migration status: {e}")
        return False


def main():
    """Main initialization function."""
    logger.info("🚀 Starting database initialization...")
    
    # Wait for database to be available
    if not wait_for_database():
        logger.error("❌ Database initialization failed - database not available")
        sys.exit(1)
    
    # Check current migration status
    check_migration_status()
    
    # Run migrations
    if not run_migrations():
        logger.error("❌ Database initialization failed - migration error")
        sys.exit(1)
    
    # Final status check
    check_migration_status()
    
    logger.info("🎉 Database initialization completed successfully!")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time} | {level} | {message}")
    
    main()
