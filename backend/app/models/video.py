from sqlalchemy import Column, String, Integer, DateTime, Float, JSO<PERSON>, Boolean, Text, Enum as SQLEnum
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class VideoStatus(str, enum.Enum):
    UPLOADING = "uploading"
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    INDEXED = "indexed"
    FAILED = "failed"


class EmbeddingModel(str, enum.Enum):
    NOVA_PREMIER = "nova-premier"
    MARENGO = "marengo"


class Video(Base):
    __tablename__ = "videos"
    
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(String, unique=True, index=True, nullable=False)
    filename = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    s3_key = Column(String, nullable=False)
    s3_uri = Column(String, nullable=False)
    
    # Status
    status = Column(SQLEnum(VideoStatus), default=VideoStatus.UPLOADING, nullable=False)
    
    # Metadata
    file_size = Column(Integer)  # bytes
    duration = Column(Float)  # seconds
    width = Column(Integer)
    height = Column(Integer)
    fps = Column(Float)
    
    # Processing info
    processing_error = Column(Text, nullable=True)
    
    # Task timing - Upload
    upload_started_at = Column(DateTime(timezone=True), nullable=True)
    upload_completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Task timing - Nova Premier Embedding
    nova_embedding_started_at = Column(DateTime(timezone=True), nullable=True)
    nova_embedding_completed_at = Column(DateTime(timezone=True), nullable=True)
    nova_embedding_arn = Column(String, nullable=True)
    nova_indexed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Task timing - Marengo Embedding
    marengo_embedding_started_at = Column(DateTime(timezone=True), nullable=True)
    marengo_embedding_completed_at = Column(DateTime(timezone=True), nullable=True)
    marengo_embedding_arn = Column(String, nullable=True)
    marengo_indexed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Task timing - Summary
    summary_started_at = Column(DateTime(timezone=True), nullable=True)
    summary_completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Summary (from Nova Premier)
    summary = Column(Text, nullable=True)
    risk_score = Column(Float, nullable=True)
    
    # YOLOv8 Object Detections
    object_detections = Column(JSON, nullable=True)  # Stores detected objects and counts
    
    # PII Redaction
    pii_redacted = Column(Boolean, default=False)  # Whether PII has been redacted
    pii_redacted_s3_key = Column(String, nullable=True)  # S3 key for redacted version
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Video {self.video_id}>"


class ProcessingJob(Base):
    __tablename__ = "processing_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(String, unique=True, index=True, nullable=False)
    video_id = Column(String, index=True, nullable=False)
    
    # Job details
    job_type = Column(String, nullable=False)  # embedding, summary, processing
    status = Column(String, default="pending", nullable=False)  # pending, running, completed, failed
    
    # AWS Bedrock specific
    invocation_arn = Column(String, nullable=True)
    
    # Progress
    progress = Column(Float, default=0.0)
    message = Column(Text, nullable=True)
    
    # Results
    result_s3_key = Column(String, nullable=True)
    error = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<ProcessingJob {self.job_id}>"


class SearchRating(Base):
    __tablename__ = "search_ratings"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Search details
    query = Column(Text, nullable=False)
    query_hash = Column(String, index=True, nullable=False)
    
    # Result details
    video_id = Column(String, index=True, nullable=False)
    segment_id = Column(Integer, nullable=False)
    
    # Model used
    model_type = Column(SQLEnum(EmbeddingModel), nullable=False)
    
    # Rating
    rating = Column(Integer, nullable=False)  # 1-5
    similarity_score = Column(Float, nullable=False)
    
    # Session
    user_session = Column(String, nullable=True)
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<SearchRating {self.id}>"
