from celery import Celery
from app.core.config import settings

celery_app = Celery(
    "samsara",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
)

celery_app.autodiscover_tasks(
    [
        "app.tasks.video_tasks",
        "app.tasks.embedding_tasks",
        "app.tasks.youtube_tasks",
        "app.tasks.workflow_tasks",
        "app.tasks.summary_tasks",
        "app.tasks.clip_generation_tasks"  # NEW: Clip pre-generation
    ]
)

# Import new workflow tasks to register them
from app.tasks import workflow_tasks, summary_tasks, clip_generation_tasks  # noqa: F401

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=3600,  # 1 hour
    task_soft_time_limit=3300,  # 55 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=50,
    
    # Single queue for all tasks (consolidated workers)
    task_default_queue='celery',
    task_default_exchange='celery',
    task_default_routing_key='celery',
)

# Periodic tasks (if needed)
celery_app.conf.beat_schedule = {
    "check-embedding-jobs": {
        "task": "app.tasks.embedding_tasks.check_bedrock_jobs",
        "schedule": 30.0,  # Every 30 seconds
    },
}
