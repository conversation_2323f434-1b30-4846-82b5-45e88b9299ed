from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # Application
    APP_NAME: str = "Samsara Video Search POC"
    APP_VERSION: str = "2.0.0"
    DEBUG: bool = True
    
    # Authentication (simple password-based)
    SECRET_KEY: str = "minfy2025-secret-key-change-in-production"
    ADMIN_PASSWORD: str = "minfy2025"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 24 hours
    
    # Database
    DATABASE_URL: str = "***************************************************/samsara_db"
    
    # Redis
    REDIS_URL: str = "redis://redis:6379/0"
    
    # Celery
    CELERY_BROKER_URL: str = "redis://redis:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://redis:6379/1"
    
    # Weaviate
    WEAVIATE_URL: str = "http://weaviate:8080"
    WEAVIATE_COLLECTION: str = "VideoEmbeddings"
    
    # AWS Configuration
    AWS_ACCESS_KEY_ID: str
    AWS_SECRET_ACCESS_KEY: str
    AWS_ACCOUNT_ID: str
    S3_BUCKET: str = "poc-video-search-bucket"
    BEDROCK_REGION: str = "us-east-1"
    
    # Video Processing
    MAX_UPLOAD_SIZE: int = 2 * 1024 * 1024 * 1024  # 2GB
    SUPPORTED_VIDEO_FORMATS: list = [".mp4", ".mov", ".avi", ".mkv"]
    YOLOV8_MODEL: str = "yolov8n.pt"
    
    # Embedding Models
    EMBEDDING_MODELS: dict = {
        "nova-premier": "us.amazon.nova-premier-v1:0",
        "marengo": "twelvelabs.marengo-embed-2-7-v1:0"
    }
    
    # CORS
    CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:8000",
    ]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
