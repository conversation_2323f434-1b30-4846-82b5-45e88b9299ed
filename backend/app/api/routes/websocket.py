from fastapi import <PERSON><PERSON><PERSON>er, WebSocket, WebSocketDisconnect, Depends
from typing import Dict, Set
from loguru import logger
import json
import asyncio

router = APIRouter(tags=["WebSocket"])


class ConnectionManager:
    """Manage WebSocket connections"""
    
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept and store connection"""
        await websocket.accept()
        if client_id not in self.active_connections:
            self.active_connections[client_id] = set()
        self.active_connections[client_id].add(websocket)
        logger.info(f"WebSocket connected: {client_id}")
    
    def disconnect(self, websocket: WebSocket, client_id: str):
        """Remove connection"""
        if client_id in self.active_connections:
            self.active_connections[client_id].discard(websocket)
            if not self.active_connections[client_id]:
                del self.active_connections[client_id]
        logger.info(f"WebSocket disconnected: {client_id}")
    
    async def send_personal_message(self, message: dict, client_id: str):
        """Send message to specific client"""
        if client_id in self.active_connections:
            disconnected = set()
            for connection in self.active_connections[client_id]:
                try:
                    await connection.send_json(message)
                except:
                    disconnected.add(connection)
            
            # Clean up disconnected
            for conn in disconnected:
                self.active_connections[client_id].discard(conn)
    
    async def broadcast(self, message: dict):
        """Broadcast message to all clients"""
        for client_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, client_id)


manager = ConnectionManager()


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time updates"""
    await manager.connect(websocket, client_id)
    
    try:
        # Send welcome message
        await websocket.send_json({
            "type": "connection",
            "status": "connected",
            "client_id": client_id,
            "message": "WebSocket connection established"
        })
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_json({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    })
                
                elif message.get("type") == "subscribe":
                    # Subscribe to specific events (e.g., video_id, job_id)
                    await websocket.send_json({
                        "type": "subscribed",
                        "subscription": message.get("subscription")
                    })
                
                elif message.get("type") == "unsubscribe":
                    await websocket.send_json({
                        "type": "unsubscribed",
                        "subscription": message.get("subscription")
                    })
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error",
                    "message": "Invalid JSON format"
                })
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                await websocket.send_json({
                    "type": "error",
                    "message": str(e)
                })
    
    except WebSocketDisconnect:
        manager.disconnect(websocket, client_id)
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        manager.disconnect(websocket, client_id)


async def notify_job_update(job_id: str, video_id: str, status: str, progress: float, message: str = None):
    """Notify clients about job updates"""
    await manager.broadcast({
        "type": "job_update",
        "job_id": job_id,
        "video_id": video_id,
        "status": status,
        "progress": progress,
        "message": message
    })


async def notify_video_update(video_id: str, status: str, message: str = None):
    """Notify clients about video updates"""
    await manager.broadcast({
        "type": "video_update",
        "video_id": video_id,
        "status": status,
        "message": message
    })


async def notify_upload_progress(video_id: str, progress: float, message: str = None):
    """Notify clients about upload progress"""
    await manager.broadcast({
        "type": "upload_progress",
        "video_id": video_id,
        "progress": progress,
        "message": message
    })
