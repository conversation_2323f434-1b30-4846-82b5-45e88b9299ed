from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from loguru import logger

from app.api.dependencies import get_current_user, get_db_session
from app.models.video import ProcessingJob
from app.schemas.video import JobStatus
from celery.result import AsyncResult
from app.core.celery_app import celery_app

router = APIRouter(prefix="/jobs", tags=["Jobs"])


@router.get("/{job_id}", response_model=JobStatus)
async def get_job_status(
    job_id: str,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get status of a specific job"""
    try:
        # Check database first
        job = db.query(ProcessingJob).filter(ProcessingJob.job_id == job_id).first()
        
        if job:
            return JobStatus(
                job_id=job.job_id,
                video_id=job.video_id,
                job_type=job.job_type,
                status=job.status,
                progress=job.progress,
                message=job.message,
                error=job.error,
                created_at=job.created_at,
                completed_at=job.completed_at
            )
        
        # Check Celery task status
        task = AsyncResult(job_id, app=celery_app)
        
        if task.state == 'PENDING':
            status_str = 'pending'
        elif task.state == 'STARTED':
            status_str = 'running'
        elif task.state == 'SUCCESS':
            status_str = 'completed'
        elif task.state == 'FAILURE':
            status_str = 'failed'
        else:
            status_str = task.state.lower()
        
        return JobStatus(
            job_id=job_id,
            video_id='unknown',
            job_type='celery_task',
            status=status_str,
            progress=0.0,
            message=f"Celery task state: {task.state}",
            error=str(task.info) if task.state == 'FAILURE' else None,
            created_at=None,
            completed_at=None
        )
        
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get job status: {str(e)}"
        )


@router.get("/video/{video_id}", response_model=List[JobStatus])
async def get_video_jobs(
    video_id: str,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get all jobs for a specific video"""
    try:
        jobs = db.query(ProcessingJob).filter(
            ProcessingJob.video_id == video_id
        ).order_by(ProcessingJob.created_at.desc()).all()
        
        return [
            JobStatus(
                job_id=job.job_id,
                video_id=job.video_id,
                job_type=job.job_type,
                status=job.status,
                progress=job.progress,
                message=job.message,
                error=job.error,
                created_at=job.created_at,
                completed_at=job.completed_at
            )
            for job in jobs
        ]
        
    except Exception as e:
        logger.error(f"Error getting video jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get video jobs: {str(e)}"
        )


@router.get("", response_model=List[JobStatus])
async def list_jobs(
    skip: int = 0,
    limit: int = 50,
    status_filter: Optional[str] = None,
    job_type_filter: Optional[str] = None,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """List all jobs with optional filters"""
    try:
        query = db.query(ProcessingJob)
        
        if status_filter:
            query = query.filter(ProcessingJob.status == status_filter)
        
        if job_type_filter:
            query = query.filter(ProcessingJob.job_type == job_type_filter)
        
        jobs = query.order_by(ProcessingJob.created_at.desc()).offset(skip).limit(limit).all()
        
        return [
            JobStatus(
                job_id=job.job_id,
                video_id=job.video_id,
                job_type=job.job_type,
                status=job.status,
                progress=job.progress,
                message=job.message,
                error=job.error,
                created_at=job.created_at,
                completed_at=job.completed_at
            )
            for job in jobs
        ]
        
    except Exception as e:
        logger.error(f"Error listing jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list jobs: {str(e)}"
        )


@router.get("/stats/summary")
async def get_job_statistics(
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get job statistics summary"""
    try:
        from sqlalchemy import func
        
        # Total jobs
        total_jobs = db.query(func.count(ProcessingJob.id)).scalar()
        
        # Jobs by status
        status_counts = db.query(
            ProcessingJob.status,
            func.count(ProcessingJob.id).label('count')
        ).group_by(ProcessingJob.status).all()
        
        # Jobs by type
        type_counts = db.query(
            ProcessingJob.job_type,
            func.count(ProcessingJob.id).label('count')
        ).group_by(ProcessingJob.job_type).all()
        
        # Running jobs
        running_jobs = db.query(ProcessingJob).filter(
            ProcessingJob.status == 'running'
        ).all()
        
        return {
            "total_jobs": total_jobs,
            "status_distribution": [
                {"status": s.status, "count": s.count}
                for s in status_counts
            ],
            "type_distribution": [
                {"type": t.job_type, "count": t.count}
                for t in type_counts
            ],
            "running_jobs": [
                {
                    "job_id": job.job_id,
                    "video_id": job.video_id,
                    "job_type": job.job_type,
                    "progress": job.progress,
                    "message": job.message
                }
                for job in running_jobs
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting job statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get job statistics: {str(e)}"
        )
