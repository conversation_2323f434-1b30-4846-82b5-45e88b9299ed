from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, case
from typing import List, Dict, Any
from datetime import datetime, timedelta
from loguru import logger

from app.api.dependencies import get_current_user, get_db_session
from app.models.video import SearchRating, Video, EmbeddingModel, ProcessingJob
from app.schemas.video import RatingResponse

router = APIRouter(prefix="/analytics", tags=["Analytics"])


@router.get("/dashboard")
async def get_dashboard_analytics(
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get comprehensive dashboard analytics"""
    try:
        from datetime import datetime, timedelta
        from app.models.video import ProcessingJob

        # System Overview
        total_videos = db.query(func.count(Video.id)).scalar() or 0
        total_ratings = db.query(func.count(SearchRating.id)).scalar() or 0

        # Recent activity (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_videos = db.query(func.count(Video.id)).filter(
            Video.created_at >= week_ago
        ).scalar() or 0
        recent_ratings = db.query(func.count(SearchRating.id)).filter(
            SearchRating.created_at >= week_ago
        ).scalar() or 0

        # Model Performance
        model_stats = []
        for model in [EmbeddingModel.NOVA_PREMIER, EmbeddingModel.MARENGO]:
            stats = db.query(
                func.count(SearchRating.id).label('total_searches'),
                func.avg(SearchRating.rating).label('avg_rating'),
                func.avg(SearchRating.similarity_score).label('avg_similarity')
            ).filter(SearchRating.model_type == model).first()

            model_stats.append({
                "model": model.value,
                "total_searches": stats.total_searches or 0,
                "average_rating": round(float(stats.avg_rating or 0), 2),
                "average_similarity": round(float(stats.avg_similarity or 0), 3)
            })

        # Processing Jobs Status
        job_stats = db.query(
            ProcessingJob.status,
            func.count(ProcessingJob.id).label('count')
        ).group_by(ProcessingJob.status).all()

        processing_stats = {
            "completed": 0,
            "running": 0,
            "failed": 0,
            "pending": 0
        }

        for stat in job_stats:
            if stat.status in processing_stats:
                processing_stats[stat.status] = stat.count

        # Search Performance (last 24 hours)
        day_ago = datetime.utcnow() - timedelta(days=1)
        recent_search_stats = db.query(
            func.count(SearchRating.id).label('total_searches'),
            func.avg(SearchRating.rating).label('avg_rating')
        ).filter(SearchRating.created_at >= day_ago).first()

        # Top performing queries
        top_queries = db.query(
            SearchRating.query,
            func.avg(SearchRating.rating).label('avg_rating'),
            func.count(SearchRating.id).label('search_count')
        ).group_by(SearchRating.query).having(
            func.count(SearchRating.id) >= 2
        ).order_by(func.avg(SearchRating.rating).desc()).limit(10).all()

        return {
            "overview": {
                "total_videos": total_videos,
                "total_searches": total_ratings,
                "recent_videos": recent_videos,
                "recent_searches": recent_ratings
            },
            "model_performance": model_stats,
            "processing_status": processing_stats,
            "search_performance": {
                "last_24h_searches": recent_search_stats.total_searches or 0,
                "last_24h_avg_rating": round(float(recent_search_stats.avg_rating or 0), 2)
            },
            "top_queries": [
                {
                    "query": q.query,
                    "average_rating": round(float(q.avg_rating), 2),
                    "search_count": q.search_count
                }
                for q in top_queries
            ]
        }

    except Exception as e:
        logger.error(f"Dashboard analytics error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get dashboard analytics: {str(e)}"
        )


@router.get("/ratings")
async def get_ratings_analytics(
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get rating statistics and analytics"""
    try:
        # Total ratings
        total_ratings = db.query(func.count(SearchRating.id)).scalar()
        
        # Average rating by model
        model_stats = db.query(
            SearchRating.model_type,
            func.count(SearchRating.id).label('count'),
            func.avg(SearchRating.rating).label('avg_rating'),
            func.avg(SearchRating.similarity_score).label('avg_similarity')
        ).group_by(SearchRating.model_type).all()
        
        # Rating distribution
        rating_distribution = db.query(
            SearchRating.rating,
            func.count(SearchRating.id).label('count')
        ).group_by(SearchRating.rating).all()
        
        # Recent ratings
        recent_ratings = db.query(SearchRating).order_by(
            SearchRating.created_at.desc()
        ).limit(10).all()
        
        return {
            "total_ratings": total_ratings,
            "model_statistics": [
                {
                    "model": stat.model_type.value,
                    "count": stat.count,
                    "average_rating": round(float(stat.avg_rating), 2),
                    "average_similarity": round(float(stat.avg_similarity), 3)
                }
                for stat in model_stats
            ],
            "rating_distribution": [
                {
                    "rating": dist.rating,
                    "count": dist.count
                }
                for dist in rating_distribution
            ],
            "recent_ratings": [
                {
                    "id": r.id,
                    "query": r.query,
                    "video_id": r.video_id,
                    "rating": r.rating,
                    "model": r.model_type.value,
                    "created_at": r.created_at.isoformat()
                }
                for r in recent_ratings
            ]
        }
        
    except Exception as e:
        logger.error(f"Analytics error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get analytics: {str(e)}"
        )


@router.get("/models")
async def compare_models(
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Compare performance of different embedding models"""
    try:
        # Get statistics for each model
        models_data = []
        
        for model in [EmbeddingModel.NOVA_PREMIER, EmbeddingModel.MARENGO]:
            # Rating stats
            rating_stats = db.query(
                func.count(SearchRating.id).label('total_ratings'),
                func.avg(SearchRating.rating).label('avg_rating'),
                func.avg(SearchRating.similarity_score).label('avg_similarity'),
                func.min(SearchRating.rating).label('min_rating'),
                func.max(SearchRating.rating).label('max_rating')
            ).filter(SearchRating.model_type == model).first()
            
            # Video stats - updated for dual embedding model
            video_count = db.query(func.count(Video.id)).scalar()

            # Count videos with specific model embeddings completed
            if model == EmbeddingModel.NOVA_PREMIER:
                indexed_count = db.query(func.count(Video.id)).filter(
                    Video.nova_indexed_at.isnot(None)
                ).scalar()
            else:  # MARENGO
                indexed_count = db.query(func.count(Video.id)).filter(
                    Video.marengo_indexed_at.isnot(None)
                ).scalar()
            
            models_data.append({
                "model": model.value,
                "video_count": video_count,
                "indexed_count": indexed_count,
                "rating_stats": {
                    "total_ratings": rating_stats.total_ratings or 0,
                    "average_rating": round(float(rating_stats.avg_rating), 2) if rating_stats.avg_rating else 0,
                    "average_similarity": round(float(rating_stats.avg_similarity), 3) if rating_stats.avg_similarity else 0,
                    "min_rating": rating_stats.min_rating or 0,
                    "max_rating": rating_stats.max_rating or 0
                }
            })
        
        return {
            "models": models_data,
            "comparison": {
                "total_videos": sum(m['video_count'] for m in models_data),
                "total_indexed": sum(m['indexed_count'] for m in models_data),
                "total_ratings": sum(m['rating_stats']['total_ratings'] for m in models_data)
            }
        }
        
    except Exception as e:
        logger.error(f"Model comparison error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to compare models: {str(e)}"
        )


@router.get("/model-accuracy")
async def get_model_accuracy_analytics(
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get detailed model accuracy and performance analytics"""
    try:
        # Model accuracy comparison
        model_accuracy = []
        for model in [EmbeddingModel.NOVA_PREMIER, EmbeddingModel.MARENGO]:
            # Detailed rating statistics
            rating_stats = db.query(
                func.count(SearchRating.id).label('total_searches'),
                func.avg(SearchRating.rating).label('avg_rating'),
                func.avg(SearchRating.similarity_score).label('avg_similarity'),
                func.sum(case((SearchRating.rating >= 4, 1), else_=0)).label('high_ratings'),
                func.sum(case((SearchRating.rating <= 2, 1), else_=0)).label('low_ratings')
            ).filter(SearchRating.model_type == model).first()

            # Rating distribution
            rating_dist = db.query(
                SearchRating.rating,
                func.count(SearchRating.id).label('count')
            ).filter(SearchRating.model_type == model).group_by(SearchRating.rating).all()

            # Recent performance (last 7 days)
            week_ago = datetime.utcnow() - timedelta(days=7)
            recent_stats = db.query(
                func.count(SearchRating.id).label('recent_searches'),
                func.avg(SearchRating.rating).label('recent_avg_rating')
            ).filter(
                SearchRating.model_type == model,
                SearchRating.created_at >= week_ago
            ).first()

            total_searches = rating_stats.total_searches or 0
            high_ratings = rating_stats.high_ratings or 0
            low_ratings = rating_stats.low_ratings or 0

            model_accuracy.append({
                "model": model.value,
                "accuracy_metrics": {
                    "total_searches": total_searches,
                    "average_rating": round(float(rating_stats.avg_rating or 0), 2),
                    "average_similarity": round(float(rating_stats.avg_similarity or 0), 3),
                    "satisfaction_rate": round((high_ratings / total_searches * 100), 1) if total_searches > 0 else 0,
                    "poor_performance_rate": round((low_ratings / total_searches * 100), 1) if total_searches > 0 else 0
                },
                "rating_distribution": {str(r.rating): r.count for r in rating_dist},
                "recent_performance": {
                    "searches_last_7_days": recent_stats.recent_searches or 0,
                    "avg_rating_last_7_days": round(float(recent_stats.recent_avg_rating or 0), 2)
                }
            })

        # Top and bottom performing queries
        top_queries = db.query(
            SearchRating.query,
            func.avg(SearchRating.rating).label('avg_rating'),
            func.count(SearchRating.id).label('search_count'),
            SearchRating.model_type
        ).group_by(SearchRating.query, SearchRating.model_type).having(
            func.count(SearchRating.id) >= 2
        ).order_by(func.avg(SearchRating.rating).desc()).limit(10).all()

        bottom_queries = db.query(
            SearchRating.query,
            func.avg(SearchRating.rating).label('avg_rating'),
            func.count(SearchRating.id).label('search_count'),
            SearchRating.model_type
        ).group_by(SearchRating.query, SearchRating.model_type).having(
            func.count(SearchRating.id) >= 2
        ).order_by(func.avg(SearchRating.rating).asc()).limit(5).all()

        return {
            "model_accuracy": model_accuracy,
            "query_performance": {
                "top_performing": [
                    {
                        "query": q.query,
                        "avg_rating": round(float(q.avg_rating), 2),
                        "search_count": q.search_count,
                        "model": q.model_type.value
                    }
                    for q in top_queries
                ],
                "poor_performing": [
                    {
                        "query": q.query,
                        "avg_rating": round(float(q.avg_rating), 2),
                        "search_count": q.search_count,
                        "model": q.model_type.value
                    }
                    for q in bottom_queries
                ]
            }
        }

    except Exception as e:
        logger.error(f"Model accuracy analytics error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get model accuracy analytics: {str(e)}"
        )


@router.get("/processing-performance")
async def get_processing_performance_analytics(
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get processing time and performance analytics"""
    try:
        # Processing time analytics
        videos_with_times = db.query(Video).filter(
            and_(
                Video.upload_started_at.isnot(None),
                Video.upload_completed_at.isnot(None)
            )
        ).all()

        processing_metrics = {
            "upload_times": [],
            "nova_embedding_times": [],
            "marengo_embedding_times": [],
            "summary_times": [],
            "total_processing_times": []
        }

        for video in videos_with_times:
            # Upload time
            if video.upload_started_at and video.upload_completed_at:
                upload_time = (video.upload_completed_at - video.upload_started_at).total_seconds()
                processing_metrics["upload_times"].append(upload_time)

            # Nova embedding time
            if video.nova_embedding_started_at and video.nova_embedding_completed_at:
                nova_time = (video.nova_embedding_completed_at - video.nova_embedding_started_at).total_seconds()
                processing_metrics["nova_embedding_times"].append(nova_time)

            # Marengo embedding time
            if video.marengo_embedding_started_at and video.marengo_embedding_completed_at:
                marengo_time = (video.marengo_embedding_completed_at - video.marengo_embedding_started_at).total_seconds()
                processing_metrics["marengo_embedding_times"].append(marengo_time)

            # Summary time
            if video.summary_started_at and video.summary_completed_at:
                summary_time = (video.summary_completed_at - video.summary_started_at).total_seconds()
                processing_metrics["summary_times"].append(summary_time)

            # Total processing time (upload to indexed)
            if video.upload_started_at and video.nova_indexed_at and video.marengo_indexed_at:
                # Use the later of the two indexed times
                final_indexed = max(video.nova_indexed_at, video.marengo_indexed_at)
                total_time = (final_indexed - video.upload_started_at).total_seconds()
                processing_metrics["total_processing_times"].append(total_time)

        # Calculate statistics for each metric
        def calculate_stats(times_list):
            if not times_list:
                return {"count": 0, "avg": 0, "min": 0, "max": 0, "median": 0}

            times_list.sort()
            count = len(times_list)
            avg = sum(times_list) / count
            median = times_list[count // 2] if count % 2 == 1 else (times_list[count // 2 - 1] + times_list[count // 2]) / 2

            return {
                "count": count,
                "avg": round(avg, 2),
                "min": round(min(times_list), 2),
                "max": round(max(times_list), 2),
                "median": round(median, 2)
            }

        performance_stats = {
            "upload": calculate_stats(processing_metrics["upload_times"]),
            "nova_embedding": calculate_stats(processing_metrics["nova_embedding_times"]),
            "marengo_embedding": calculate_stats(processing_metrics["marengo_embedding_times"]),
            "summary_generation": calculate_stats(processing_metrics["summary_times"]),
            "total_processing": calculate_stats(processing_metrics["total_processing_times"])
        }

        # Processing job statistics
        job_stats = db.query(
            ProcessingJob.status,
            func.count(ProcessingJob.id).label('count')
        ).group_by(ProcessingJob.status).all()

        job_statistics = {stat.status: stat.count for stat in job_stats}

        # Recent processing performance (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_videos = db.query(Video).filter(
            Video.created_at >= week_ago
        ).count()

        recent_indexed = db.query(Video).filter(
            and_(
                Video.created_at >= week_ago,
                Video.status == 'indexed'
            )
        ).count()

        success_rate = round((recent_indexed / recent_videos * 100), 1) if recent_videos > 0 else 0

        return {
            "processing_performance": performance_stats,
            "job_statistics": job_statistics,
            "recent_metrics": {
                "videos_last_7_days": recent_videos,
                "successfully_indexed": recent_indexed,
                "success_rate_percent": success_rate
            },
            "efficiency_insights": {
                "fastest_upload": performance_stats["upload"]["min"],
                "fastest_embedding": min(
                    performance_stats["nova_embedding"]["min"] if performance_stats["nova_embedding"]["count"] > 0 else float('inf'),
                    performance_stats["marengo_embedding"]["min"] if performance_stats["marengo_embedding"]["count"] > 0 else float('inf')
                ) if performance_stats["nova_embedding"]["count"] > 0 or performance_stats["marengo_embedding"]["count"] > 0 else 0,
                "avg_total_processing": performance_stats["total_processing"]["avg"]
            }
        }

    except Exception as e:
        logger.error(f"Processing performance analytics error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get processing performance analytics: {str(e)}"
        )


@router.get("/videos")
async def get_video_statistics(
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get video statistics"""
    try:
        # Total videos
        total_videos = db.query(func.count(Video.id)).scalar()
        
        # Videos by status
        status_counts = db.query(
            Video.status,
            func.count(Video.id).label('count')
        ).group_by(Video.status).all()
        
        # Total storage
        total_size = db.query(func.sum(Video.file_size)).scalar() or 0
        
        # Average video duration
        avg_duration = db.query(func.avg(Video.duration)).scalar() or 0
        
        # Videos with summaries
        videos_with_summary = db.query(func.count(Video.id)).filter(
            Video.summary.isnot(None)
        ).scalar()
        
        return {
            "total_videos": total_videos,
            "status_distribution": [
                {
                    "status": status.status.value,
                    "count": status.count
                }
                for status in status_counts
            ],
            "storage": {
                "total_bytes": int(total_size),
                "total_gb": round(total_size / (1024**3), 2)
            },
            "average_duration_seconds": round(float(avg_duration), 2),
            "videos_with_summary": videos_with_summary
        }
        
    except Exception as e:
        logger.error(f"Video statistics error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get video statistics: {str(e)}"
        )


@router.get("/dashboard")
async def get_dashboard_data(
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get comprehensive dashboard data"""
    try:
        # Combine all analytics
        ratings = await get_ratings_analytics(db, current_user)
        models = await compare_models(db, current_user)
        videos = await get_video_statistics(db, current_user)
        
        from datetime import datetime
        
        return {
            "ratings": ratings,
            "models": models,
            "videos": videos,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Dashboard data error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get dashboard data: {str(e)}"
        )
