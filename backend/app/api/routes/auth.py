from fastapi import APIRouter, HTTPException, status
from datetime import timedelta
from app.schemas.auth import LoginRequest, TokenResponse
from app.core.security import authenticate, create_access_token
from app.core.config import settings

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/login", response_model=TokenResponse)
async def login(request: LoginRequest):
    """Simple password-based login"""
    if not authenticate(request.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": "admin"},
        expires_delta=access_token_expires
    )
    
    return TokenResponse(access_token=access_token)


@router.post("/verify")
async def verify_token(token: str):
    """Verify token validity"""
    from app.core.security import verify_token
    from fastapi.security import HTTPAuthorizationCredentials
    
    try:
        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)
        payload = verify_token(credentials)
        return {"valid": True, "payload": payload}
    except:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
