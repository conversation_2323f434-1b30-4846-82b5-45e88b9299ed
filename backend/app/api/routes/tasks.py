"""Task monitoring and management routes"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from celery.result import AsyncResult
from loguru import logger

from app.api.dependencies import get_db_session, get_current_user
from app.models.video import Video, VideoStatus
from app.core.celery_app import celery_app
from app.core.database import SessionLocal


router = APIRouter(prefix="/tasks", tags=["tasks"])


@router.post("/retry/{video_id}/{task_type}")
async def retry_task(
    video_id: str,
    task_type: str,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Manually retry a specific task for a video"""
    try:
        logger.info(f"Manual retry requested: {video_id} - {task_type}")
        
        # Get video
        video = db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Trigger appropriate task
        if task_type == "summary":
            from app.tasks.summary_tasks import generate_summary_from_embeddings
            task = generate_summary_from_embeddings.delay(video_id)
            logger.info(f"Triggered summary generation: {task.id}")
            return {"status": "triggered", "task_id": task.id, "task_type": "summary"}
            
        elif task_type == "clip_generation":
            from app.tasks.clip_generation_tasks import generate_all_segment_clips
            task = generate_all_segment_clips.delay(video_id, video.s3_key)
            logger.info(f"Triggered clip generation: {task.id}")
            return {"status": "triggered", "task_id": task.id, "task_type": "clip_generation"}
            
        elif task_type == "nova_embedding":
            from app.tasks.workflow_tasks import trigger_dual_embeddings
            task = trigger_dual_embeddings.delay(video_id, video.s3_key)
            logger.info(f"Triggered embeddings: {task.id}")
            return {"status": "triggered", "task_id": task.id, "task_type": "embeddings"}
            
        else:
            raise HTTPException(status_code=400, detail=f"Invalid task type: {task_type}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrying task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/active")
async def get_active_tasks(current_user: dict = Depends(get_current_user)):
    """Get all active Celery tasks"""
    try:
        # Get active tasks from Celery
        inspect = celery_app.control.inspect()
        
        active = inspect.active()
        scheduled = inspect.scheduled()
        reserved = inspect.reserved()
        
        # Format response
        tasks = {
            "active": active or {},
            "scheduled": scheduled or {},
            "reserved": reserved or {},
            "total_active": sum(len(tasks) for tasks in (active or {}).values()),
            "total_scheduled": sum(len(tasks) for tasks in (scheduled or {}).values()),
            "total_reserved": sum(len(tasks) for tasks in (reserved or {}).values()),
        }
        
        return tasks
        
    except Exception as e:
        logger.error(f"Error getting active tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{task_id}")
async def get_task_status(task_id: str) -> Dict[str, Any]:
    """Get status of a specific task"""
    try:
        result = AsyncResult(task_id, app=celery_app)
        
        response = {
            "task_id": task_id,
            "status": result.state,
            "ready": result.ready(),
            "successful": result.successful() if result.ready() else None,
            "failed": result.failed() if result.ready() else None,
        }
        
        # Add result or error info
        if result.ready():
            if result.successful():
                response["result"] = result.result
            elif result.failed():
                response["error"] = str(result.info)
        else:
            # Check for progress info
            if result.info and isinstance(result.info, dict):
                response["progress"] = result.info
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/video/{video_id}")
async def get_video_tasks(video_id: str) -> Dict[str, Any]:
    """Get all tasks associated with a video"""
    try:
        db = SessionLocal()
        video = db.query(Video).filter(Video.video_id == video_id).first()
        
        if not video:
            db.close()
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Build task status
        tasks = {
            "video_id": video_id,
            "video_status": video.status.value if video.status else "unknown",
            "filename": video.filename,
            "tasks": []
        }
        
        # Check upload task
        upload_duration = None
        if video.upload_started_at and video.upload_completed_at:
            upload_duration = (video.upload_completed_at - video.upload_started_at).total_seconds()
        
        # If video status is not "uploading", upload must be completed
        upload_status = "completed" if video.status != "uploading" else "in_progress"
        
        tasks["tasks"].append({
            "type": "upload",
            "status": upload_status,
            "started_at": video.upload_started_at.isoformat() if video.upload_started_at else None,
            "completed_at": video.upload_completed_at.isoformat() if video.upload_completed_at else None,
            "duration_seconds": upload_duration
        })
        
        # Nova Premier Embedding
        nova_started = video.nova_embedding_started_at if hasattr(video, 'nova_embedding_started_at') else None
        nova_completed = video.nova_indexed_at if hasattr(video, 'nova_indexed_at') else None
        nova_status = "pending"
        nova_duration = None
        
        if nova_started:
            if nova_completed:
                nova_status = "completed"
                nova_duration = (nova_completed - nova_started).total_seconds()
            else:
                nova_status = "in_progress"
        
        # Get Nova chunk count if completed
        nova_chunk_count = 0
        if nova_completed:
            from app.services.vector_service import VectorService
            vector_service = VectorService()
            nova_embeddings = vector_service.get_video_embeddings(video_id, model="nova")
            nova_chunk_count = len(nova_embeddings)
        
        tasks["tasks"].append({
            "type": "nova_embedding",
            "status": nova_status,
            "has_result": bool(nova_completed),
            "started_at": nova_started.isoformat() if nova_started else None,
            "completed_at": nova_completed.isoformat() if nova_completed else None,
            "duration_seconds": nova_duration,
            "chunk_count": nova_chunk_count
        })
        
        # Marengo Embedding
        marengo_started = video.marengo_embedding_started_at if hasattr(video, 'marengo_embedding_started_at') else None
        marengo_completed = video.marengo_indexed_at if hasattr(video, 'marengo_indexed_at') else None
        marengo_status = "pending"
        marengo_duration = None
        
        if marengo_started:
            if marengo_completed:
                marengo_status = "completed"
                marengo_duration = (marengo_completed - marengo_started).total_seconds()
            else:
                marengo_status = "in_progress"
        
        # Get Marengo chunk count if completed
        marengo_chunk_count = 0
        if marengo_completed:
            from app.services.vector_service import VectorService
            vector_service = VectorService()
            marengo_embeddings = vector_service.get_video_embeddings(video_id, model="marengo")
            marengo_chunk_count = len(marengo_embeddings)
        
        tasks["tasks"].append({
            "type": "marengo_embedding",
            "status": marengo_status,
            "has_result": bool(marengo_completed),
            "started_at": marengo_started.isoformat() if marengo_started else None,
            "completed_at": marengo_completed.isoformat() if marengo_completed else None,
            "duration_seconds": marengo_duration,
            "chunk_count": marengo_chunk_count
        })
        
        # Check summary
        summary_duration = None
        if hasattr(video, 'summary_started_at') and video.summary_started_at and hasattr(video, 'summary_completed_at') and video.summary_completed_at:
            summary_duration = (video.summary_completed_at - video.summary_started_at).total_seconds()
        
        # Determine summary status
        summary_status = "pending"
        if video.summary:
            summary_status = "completed"
        elif hasattr(video, 'summary_started_at') and video.summary_started_at:
            summary_status = "in_progress"
        
        tasks["tasks"].append({
            "type": "summary",
            "status": summary_status,
            "has_result": bool(video.summary),
            "started_at": video.summary_started_at.isoformat() if hasattr(video, 'summary_started_at') and video.summary_started_at else None,
            "completed_at": video.summary_completed_at.isoformat() if hasattr(video, 'summary_completed_at') and video.summary_completed_at else None,
            "duration_seconds": summary_duration
        })
        
        # Clip Generation (NEW)
        from app.models.video import ProcessingJob
        clip_job = db.query(ProcessingJob).filter(
            ProcessingJob.video_id == video_id,
            ProcessingJob.job_type == "clip_generation"
        ).order_by(ProcessingJob.created_at.desc()).first()
        
        clip_status = "pending"
        clip_progress = None
        clip_message = None
        clip_started = None
        clip_completed = None
        clip_duration = None
        
        if clip_job:
            clip_status = clip_job.status
            clip_progress = {"percent": clip_job.progress, "message": clip_job.message}
            clip_started = clip_job.started_at
            clip_completed = clip_job.completed_at
            if clip_started and clip_completed:
                clip_duration = (clip_completed - clip_started).total_seconds()
        
        # Count generated clips in S3
        clip_count = 0
        if clip_status == "completed":
            try:
                from app.services.aws_service import aws_service
                clip_prefix = f"clips/{video_id}/"
                # List all clips for this video
                response = aws_service.s3_client.list_objects_v2(
                    Bucket=aws_service.s3_bucket,
                    Prefix=clip_prefix
                )
                if 'Contents' in response:
                    # Count .mp4 files (exclude directories)
                    clip_count = sum(1 for obj in response['Contents'] if obj['Key'].endswith('.mp4'))
            except Exception as e:
                logger.warning(f"Failed to count clips: {e}")
        
        tasks["tasks"].append({
            "type": "clip_generation",
            "status": clip_status,
            "has_result": clip_status == "completed",
            "started_at": clip_started.isoformat() if clip_started else None,
            "completed_at": clip_completed.isoformat() if clip_completed else None,
            "duration_seconds": clip_duration,
            "progress": clip_progress,
            "clip_count": clip_count
        })
        
        # NOTE: Object detection removed from upload workflow
        # It's now performed on-demand during semantic search only
        
        db.close()
        return tasks
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting video tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_task_stats() -> Dict[str, Any]:
    """Get overall task statistics"""
    try:
        inspect = celery_app.control.inspect()
        
        stats = inspect.stats()
        active = inspect.active()
        
        # Get database stats
        db = SessionLocal()
        total_videos = db.query(Video).count()
        indexed_videos = db.query(Video).filter(Video.status == "indexed").count()
        processing_videos = db.query(Video).filter(Video.status == "processing").count()
        failed_videos = db.query(Video).filter(Video.processing_error.isnot(None)).count()
        
        db.close()
        
        return {
            "celery": {
                "workers": len(stats) if stats else 0,
                "active_tasks": sum(len(tasks) for tasks in (active or {}).values()),
            },
            "videos": {
                "total": total_videos,
                "indexed": indexed_videos,
                "processing": processing_videos,
                "failed": failed_videos,
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting task stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/retry/{task_id}")
async def retry_task(task_id: str) -> Dict[str, str]:
    """Retry a failed task"""
    try:
        result = AsyncResult(task_id, app=celery_app)
        
        if not result.failed():
            raise HTTPException(status_code=400, detail="Task has not failed")
        
        # Retry the task
        result.retry()
        
        return {"message": "Task retry initiated", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrying task: {e}")
        raise HTTPException(status_code=500, detail=str(e))
