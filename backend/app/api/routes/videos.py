from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uuid
import io
import tempfile
from datetime import datetime
from loguru import logger

from app.api.dependencies import get_current_user, get_db_session
from app.models.video import Video, VideoStatus, EmbeddingModel, ProcessingJob
from app.schemas.video import VideoResponse, VideoUploadResponse, JobStatus
from app.services.aws_service import aws_service
from app.core.config import settings

router = APIRouter(prefix="/videos", tags=["Videos"])


@router.post("/upload", response_model=VideoUploadResponse)
async def upload_video(
    file: UploadFile = File(...),
    embedding_model: str = Form(default="nova-premier"),
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Upload video file"""
    try:
        # Validate file type
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in settings.SUPPORTED_VIDEO_FORMATS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported file format. Supported: {settings.SUPPORTED_VIDEO_FORMATS}"
            )
        
        # Generate video ID
        video_id = str(uuid.uuid4())[:12]
        
        # Sanitize filename - remove special characters that cause AWS issues
        import re
        clean_filename = file.filename.replace(' ', '_')
        # Remove special Unicode characters and keep only ASCII alphanumeric, dots, dashes, underscores
        clean_filename = re.sub(r'[^\w\.-]', '_', clean_filename, flags=re.ASCII)
        # Remove multiple consecutive underscores
        clean_filename = re.sub(r'_+', '_', clean_filename)
        
        s3_key = f'videos/{video_id}/{clean_filename}'
        
        # Get file size from content-length header or read first chunk
        file_size = 0
        if hasattr(file, 'size'):
            file_size = file.size
        else:
            # Estimate from first chunk
            first_chunk = await file.read(1024)
            await file.seek(0)  # Reset file pointer
            file_size = len(first_chunk) * 1000  # Rough estimate
        
        # Validate file size
        if file_size > settings.MAX_UPLOAD_SIZE:
            os.remove(temp_path)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Max size: {settings.MAX_UPLOAD_SIZE / (1024**3):.1f}GB"
            )
        
        # Create database record with upload start timestamp
        # Note: embedding_model removed - now using dual embeddings (Nova + Marengo)
        video = Video(
            video_id=video_id,
            filename=clean_filename,
            original_filename=file.filename,
            s3_key=s3_key,
            s3_uri=f"s3://{settings.S3_BUCKET}/{s3_key}",
            status=VideoStatus.UPLOADING,
            file_size=file_size,
            upload_started_at=datetime.utcnow()  # Capture upload start time
        )
        db.add(video)
        db.commit()
        db.refresh(video)
        
        # Stream upload to S3 directly (no temp file)
        from app.services.upload_service import upload_service
        
        try:
            upload_result = await upload_service.stream_to_s3(file, s3_key)
            logger.info(f"✅ Streaming upload complete: {upload_result}")
            
            # Update file size and completion timestamp
            video.file_size = upload_result['size']
            video.upload_completed_at = datetime.utcnow()  # Capture upload end time
            db.commit()
            
        except Exception as upload_error:
            logger.error(f"Streaming upload failed: {upload_error}")
            # Fall back to temp file method
            temp_path = f'/tmp/{video_id}_{clean_filename}'
            with open(temp_path, 'wb') as f:
                await file.seek(0)  # Reset file pointer
                content = await file.read()
                f.write(content)
            
            # Upload temp file
            aws_service.upload_file_to_s3(temp_path, s3_key)
            video.file_size = os.path.getsize(temp_path)
            video.upload_completed_at = datetime.utcnow()  # Capture upload end time
            db.commit()
        
        # Trigger new sequential workflow: Dual Embeddings → Summary
        from app.tasks.workflow_tasks import trigger_dual_embeddings
        trigger_dual_embeddings.delay(video_id, s3_key)
        
        logger.info(f"Video upload initiated: {video_id}")
        
        return VideoUploadResponse(
            video_id=video_id,
            upload_url=f"/api/videos/{video_id}",
            message="Video upload started. Processing in background."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading video: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Upload failed: {str(e)}"
        )


from pydantic import BaseModel

class YouTubeUploadRequest(BaseModel):
    youtube_url: str
    embedding_model: str = "nova-premier"

@router.post("/upload-youtube")
async def upload_from_youtube(
    request: YouTubeUploadRequest,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Download video from YouTube asynchronously"""
    try:
        # Generate video ID
        video_id = str(uuid.uuid4())[:11]
        
        # Create database record immediately with UPLOADING status
        # Note: embedding_model field removed - now using dual embeddings (Nova + Marengo)
        video = Video(
            video_id=video_id,
            filename=f"youtube_{video_id}.mp4",  # Temporary filename
            original_filename=request.youtube_url,  # Store URL as original filename
            s3_key=f"videos/{video_id}/pending.mp4",  # Temporary S3 key
            s3_uri=f"s3://{settings.S3_BUCKET}/videos/{video_id}/pending.mp4",
            file_size=0,  # Will be updated after download
            status=VideoStatus.UPLOADING,  # Downloading status
            upload_started_at=datetime.utcnow()
        )
        db.add(video)
        db.commit()
        db.refresh(video)
        
        # Trigger async YouTube download task
        logger.info(f"Triggering async YouTube download for {video_id}: {request.youtube_url}")
        from app.tasks.youtube_tasks import download_youtube_video
        download_youtube_video.delay(
            video_id=video_id,
            youtube_url=request.youtube_url,
            embedding_model=request.embedding_model
        )
        
        logger.info(f"✅ YouTube download task queued: {video_id}")
        return VideoUploadResponse(
            video_id=video_id,
            upload_url=f"/api/videos/{video_id}",
            message="YouTube download started in background. Check Videos page for status."
        )
        
    except Exception as e:
        logger.error(f"YouTube upload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start YouTube download: {str(e)}"
        )


@router.get("", response_model=List[VideoResponse])
async def list_videos(
    skip: int = 0,
    limit: int = 20,
    status_filter: Optional[str] = None,
    model_filter: Optional[str] = None,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """List all videos with optional filters"""
    query = db.query(Video)
    
    if status_filter:
        query = query.filter(Video.status == status_filter)
    
    # Note: model_filter removed - now using dual embeddings (Nova + Marengo)
    # if model_filter:
    #     query = query.filter(Video.embedding_model == model_filter)
    
    videos = query.order_by(Video.created_at.desc()).offset(skip).limit(limit).all()
    return videos


@router.get("/{video_id}", response_model=VideoResponse)
async def get_video(
    video_id: str,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get video details"""
    video = db.query(Video).filter(Video.video_id == video_id).first()
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    return video


@router.delete("/{video_id}")
async def delete_video(
    video_id: str,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Delete video and all associated data"""
    try:
        video = db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Delete from S3
        try:
            # Delete video file
            if aws_service.s3_object_exists(video.s3_key):
                aws_service.s3_client.delete_object(
                    Bucket=settings.S3_BUCKET,
                    Key=video.s3_key
                )
            
            # Delete embeddings
            embeddings_prefix = f'embeddings-output/{video_id}/'
            response = aws_service.s3_client.list_objects_v2(
                Bucket=settings.S3_BUCKET,
                Prefix=embeddings_prefix
            )
            if 'Contents' in response:
                for obj in response['Contents']:
                    aws_service.s3_client.delete_object(
                        Bucket=settings.S3_BUCKET,
                        Key=obj['Key']
                    )
            
            # Delete summary
            summary_key = f'summaries/{video_id}/safety_report.json'
            if aws_service.s3_object_exists(summary_key):
                aws_service.s3_client.delete_object(
                    Bucket=settings.S3_BUCKET,
                    Key=summary_key
                )
        except Exception as e:
            logger.warning(f"Error deleting S3 objects: {e}")
        
        # Delete from Weaviate
        try:
            from app.services.vector_service import vector_service
            vector_service.delete_by_video_id(video_id)
        except Exception as e:
            logger.warning(f"Error deleting from Weaviate: {e}")
        
        # Delete from database
        db.query(ProcessingJob).filter(ProcessingJob.video_id == video_id).delete()
        db.delete(video)
        db.commit()
        
        logger.info(f"Video deleted: {video_id}")
        return {"message": "Video deleted successfully", "video_id": video_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting video: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Delete failed: {str(e)}"
        )


@router.get("/{video_id}/download")
async def stream_video(
    video_id: str,
    db: Session = Depends(get_db_session)
):
    """Stream video for viewing in browser"""
    try:
        video = db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Get video from S3
        try:
            response = aws_service.s3_client.get_object(
                Bucket=settings.S3_BUCKET,
                Key=video.s3_key
            )
            
            # Stream the video
            def iterfile():
                chunk_size = 1024 * 1024  # 1MB chunks
                while True:
                    data = response['Body'].read(chunk_size)
                    if not data:
                        break
                    yield data
            
            return StreamingResponse(
                iterfile(),
                media_type="video/mp4",
                headers={
                    "Content-Disposition": f'inline; filename="{video.filename}"',
                    "Accept-Ranges": "bytes"
                }
            )
        except Exception as e:
            logger.error(f"Error streaming video: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to stream video"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in stream_video: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/{video_id}/index")
async def index_video(
    video_id: str,
    embedding_model: Optional[str] = "nova-premier",
    generate_summary: bool = True,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Start indexing process for a video"""
    try:
        video = db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        if video.status != VideoStatus.UPLOADED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Video must be uploaded before indexing. Current status: {video.status}"
            )
        
        # Update status
        video.status = VideoStatus.PROCESSING
        # Note: embedding_model field removed - now using dual embeddings (Nova + Marengo)
        db.commit()
        
        # Start embedding generation
        from app.tasks.embedding_tasks import start_embedding_generation
        start_embedding_generation.delay(video_id, video.s3_key, embedding_model)
        
        # Start summary generation if requested
        if generate_summary:
            from app.tasks.video_tasks import generate_video_summary
            generate_video_summary.delay(video_id, video.s3_key)
        
        logger.info(f"Indexing started for video: {video_id}")
        
        return {
            "message": "Indexing started",
            "video_id": video_id,
            "model": embedding_model,
            "summary_generation": generate_summary
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting indexing: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Indexing failed: {str(e)}"
        )


@router.get("/{video_id}/summary")
async def get_video_summary(
    video_id: str,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get AI-generated safety summary for a video"""
    video = db.query(Video).filter(Video.video_id == video_id).first()
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    if not video.summary:
        # Try to fetch from S3
        summary_key = f'summaries/{video_id}/safety_report.json'
        if aws_service.s3_object_exists(summary_key):
            summary_data = aws_service.download_json_from_s3(summary_key)
            video.summary = summary_data.get('summary')
            db.commit()
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Summary not available. Generate it first."
            )
    
    return {
        "video_id": video_id,
        "summary": video.summary,
        "risk_score": video.risk_score,
        "model": "nova-premier"
    }


@router.get("/{video_id}/stream")
async def get_video_stream_url(
    video_id: str,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Get presigned URL for video streaming"""
    video = db.query(Video).filter(Video.video_id == video_id).first()
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    try:
        url = aws_service.generate_presigned_url(video.s3_key, expiration=3600)
        return {
            "video_id": video_id,
            "stream_url": url,
            "expires_in": 3600
        }
    except Exception as e:
        logger.error(f"Error generating stream URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate stream URL"
        )
# Add this to the end of videos.py

@router.get("/{video_id}/segment/{segment_id}")
async def get_video_segment_with_pii(
    video_id: str,
    segment_id: int,
    blur_pii: bool = True,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a specific video segment with PII redaction
    
    GDPR/CCPA Compliance:
    - Automatically blurs faces
    - Redacts license plates
    - Protects personal information
    """
    import tempfile
    from app.services.pii_service import pii_service
    from app.services.vector_service import vector_service
    
    # Get video
    video = db.query(Video).filter(Video.video_id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Get segment info from Weaviate
    try:
        segment = vector_service.get_segment_by_id(video_id, segment_id)
        if not segment:
            raise HTTPException(status_code=404, detail="Segment not found")
        
        start_sec = segment.get('start_sec', 0.0)
    except Exception as e:
        logger.error(f"Error getting segment: {e}")
        start_sec = 0.0
    
    # Check if blurred version exists in cache
    cache_key = f"{video_id}_seg{segment_id}_blurred"
    cached_s3_key = f"segments/blurred/{cache_key}.mp4"
    
    if blur_pii:
        # Check if already cached in S3
        if not aws_service.s3_object_exists(cached_s3_key):
            # Download original video
            temp_input = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False).name
            temp_output = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False).name
            
            try:
                # Download from S3
                aws_service.download_from_s3(video.s3_key, temp_input)
                
                # Process segment with PII redaction
                result = pii_service.process_video_segment(
                    input_path=temp_input,
                    output_path=temp_output,
                    start_sec=start_sec,
                    duration=10.0,  # 10 second clip
                    blur_faces=True,
                    blur_plates=True
                )
                
                # Upload to S3 cache
                aws_service.upload_to_s3(temp_output, cached_s3_key)
                
                logger.info(f"✅ PII redacted segment cached: {cached_s3_key}")
                logger.info(f"Blurred {result['total_faces_blurred']} faces, {result['total_plates_blurred']} plates")
                
            finally:
                # Cleanup
                for f in [temp_input, temp_output]:
                    if os.path.exists(f):
                        os.unlink(f)
        
        # Stream blurred version from S3
        return StreamingResponse(
            aws_service.stream_from_s3(cached_s3_key),
            media_type="video/mp4",
            headers={
                "Content-Disposition": f"inline; filename=segment_{segment_id}_redacted.mp4",
                "X-PII-Redacted": "true"
            }
        )
    else:
        # Stream original without PII blurring (requires admin permission)
        # TODO: Add role-based access control
        return StreamingResponse(
            aws_service.stream_from_s3(video.s3_key, start_byte=int(start_sec * 1000000)),
            media_type="video/mp4",
            headers={
                "Content-Disposition": f"inline; filename=segment_{segment_id}.mp4",
                "X-PII-Redacted": "false"
            }
        )


@router.post("/search-by-objects")
async def search_by_objects(
    query: str,
    top_k: int = 10,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """
    Search videos by detected objects (YOLOv8)
    
    Example queries:
    - "person"
    - "car"
    - "truck"
    - "person and car"
    """
    try:
        # Parse query for object classes
        query_lower = query.lower()
        
        # Get all videos with object detections
        videos = db.query(Video).filter(
            Video.object_detections.isnot(None),
            Video.status == VideoStatus.INDEXED
        ).all()
        
        # Filter videos that contain the queried objects
        results = []
        for video in videos:
            if not video.object_detections:
                continue
            
            object_summary = video.object_detections.get('object_summary', {})
            
            # Check if any detected object matches query
            matching_objects = []
            for obj_class, count in object_summary.items():
                if obj_class.lower() in query_lower or query_lower in obj_class.lower():
                    matching_objects.append({
                        'class': obj_class,
                        'count': count
                    })
            
            if matching_objects:
                results.append({
                    'video_id': video.video_id,
                    'filename': video.filename,
                    'matching_objects': matching_objects,
                    'total_objects': sum(object_summary.values()),
                    'duration': video.duration,
                    'created_at': video.created_at.isoformat() if video.created_at else None
                })
        
        # Sort by relevance (number of matching objects)
        results.sort(key=lambda x: sum(o['count'] for o in x['matching_objects']), reverse=True)
        
        return {
            'query': query,
            'total_results': len(results),
            'results': results[:top_k]
        }
        
    except Exception as e:
        logger.error(f"Object search error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Object search failed: {str(e)}"
        )
