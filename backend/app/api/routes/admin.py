"""
Admin routes for database management
"""
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.video import Video, ProcessingJob
from app.services.vector_service import VectorService
from app.services.aws_service import aws_service
from loguru import logger

router = APIRouter(prefix="/admin", tags=["admin"])


@router.post("/clear-postgres")
async def clear_postgres(db: Session = Depends(get_db)):
    """
    Clear all data from PostgreSQL only.
    
    WARNING: This permanently deletes:
    - All videos and metadata
    - All processing jobs
    - All summaries and analysis
    
    This action cannot be undone!
    """
    try:
        logger.warning("🚨 Starting database clear operation...")
        
        # Count records before deletion
        video_count = db.query(Video).count()
        job_count = db.query(ProcessingJob).count()
        
        logger.info(f"Found {video_count} videos and {job_count} jobs to delete")
        
        # 1. Clear PostgreSQL
        logger.info("Clearing PostgreSQL database...")
        
        # Delete processing jobs first (foreign key constraint)
        deleted_jobs = db.query(ProcessingJob).delete()
        logger.info(f"Deleted {deleted_jobs} processing jobs")
        
        # Delete videos
        deleted_videos = db.query(Video).delete()
        logger.info(f"Deleted {deleted_videos} videos")
        
        db.commit()
        logger.info("✅ PostgreSQL cleared successfully")
        logger.warning("🎉 PostgreSQL clear operation completed!")
        
        return {
            "success": True,
            "message": f"Successfully deleted {deleted_videos} videos and {deleted_jobs} jobs from PostgreSQL",
            "details": {
                "videos_deleted": deleted_videos,
                "jobs_deleted": deleted_jobs
            }
        }
        
    except Exception as e:
        logger.error(f"Error clearing PostgreSQL: {e}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear PostgreSQL: {str(e)}"
        )


@router.post("/clear-weaviate")
async def clear_weaviate():
    """
    Clear all embeddings from Weaviate vector database.
    
    WARNING: This permanently deletes:
    - All Nova Premier embeddings
    - All Marengo embeddings
    - All vector search indices
    
    This action cannot be undone!
    """
    try:
        logger.warning("🚨 Starting Weaviate clear operation...")
        
        vector_service = VectorService()
        
        # Delete all objects from the collection
        vector_service.client.batch.delete_objects(
            class_name=vector_service.collection_name,
            where={
                "operator": "NotEqual",
                "path": ["video_id"],
                "valueString": "___non_existent___"  # Match all
            }
        )
        
        logger.info("✅ Weaviate cleared successfully")
        logger.warning("🎉 Weaviate clear operation completed!")
        
        return {
            "success": True,
            "message": "Successfully cleared all embeddings from Weaviate",
            "details": {
                "weaviate_cleared": True
            }
        }
        
    except Exception as e:
        logger.error(f"Error clearing Weaviate: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear Weaviate: {str(e)}"
        )


@router.post("/clear-s3")
async def clear_s3():
    """
    Clear videos and clips from S3 bucket.
    
    WARNING: This permanently deletes:
    - All original videos (videos/ prefix)
    - All pre-generated clips (clips/ prefix)
    - All search cache clips (search-cache/ prefix)
    
    This does NOT delete:
    - Other S3 buckets
    - Other prefixes in the same bucket
    - Protected files (used by other apps)
    
    This action cannot be undone!
    """
    try:
        logger.warning("🚨 Starting S3 clear operation...")
        
        # Files to exclude from deletion (used by other apps)
        protected_files = [
            "Dashcam_Disasters_｜_Unbelievable_Driver_Fails_2025_WUoIgQI2wPA.mp4",
            "Dash_Cam_Footage__Truck_Driver_Gets_Sideswiped_on_the_Highway.mp4",
            "Best_of_Car_Crashes_Compilation___USA___Canada_-_29.mp4"
        ]
        
        # Prefixes to clear (only video-related data)
        prefixes_to_clear = [
            "videos/",         # Original uploaded videos
            "clips/",          # Pre-generated clips
            "search-cache/"    # Search cache clips
        ]
        
        total_deleted = 0
        total_skipped = 0
        
        for prefix in prefixes_to_clear:
            logger.info(f"Clearing S3 prefix: {prefix}")
            
            # List all objects with this prefix
            paginator = aws_service.s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(
                Bucket=aws_service.s3_bucket,
                Prefix=prefix
            )
            
            deleted_count = 0
            skipped_count = 0
            
            for page in pages:
                if 'Contents' not in page:
                    continue
                
                # Filter out protected files
                objects_to_delete = []
                for obj in page['Contents']:
                    key = obj['Key']
                    # Check if filename (not full path) matches protected files
                    filename = key.split('/')[-1]
                    if filename in protected_files:
                        logger.info(f"⚠️ Skipping protected file: {key}")
                        skipped_count += 1
                    else:
                        objects_to_delete.append({'Key': key})
                
                # Delete in batches of 1000 (S3 limit)
                if objects_to_delete:
                    response = aws_service.s3_client.delete_objects(
                        Bucket=aws_service.s3_bucket,
                        Delete={'Objects': objects_to_delete}
                    )
                    deleted_count += len(response.get('Deleted', []))
            
            logger.info(f"✅ Deleted {deleted_count} objects from {prefix} (skipped {skipped_count} protected)")
            total_deleted += deleted_count
            total_skipped += skipped_count
        
        logger.info("✅ S3 cleared successfully")
        logger.warning(f"🎉 S3 clear operation completed! Deleted: {total_deleted}, Protected: {total_skipped}")
        
        return {
            "success": True,
            "message": f"Successfully deleted {total_deleted} objects from S3 (protected {total_skipped} files)",
            "details": {
                "total_objects_deleted": total_deleted,
                "total_objects_protected": total_skipped,
                "prefixes_cleared": prefixes_to_clear,
                "protected_files": protected_files
            }
        }
        
    except Exception as e:
        logger.error(f"Error clearing S3: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear S3: {str(e)}"
        )
