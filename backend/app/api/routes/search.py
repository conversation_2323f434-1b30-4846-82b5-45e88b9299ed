from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
import hashlib
import uuid
import os
from loguru import logger

from app.api.dependencies import get_current_user, get_db_session
from app.models.video import SearchRating, EmbeddingModel, Video
from app.schemas.video import SearchRequest, SearchResponse, SearchResult, RatingCreate, RatingResponse, ProcessingOptions
from app.services.aws_service import aws_service
from app.services.vector_service import vector_service
from app.services.video_service import video_service
from app.tasks.video_tasks import process_video_clip
from app.core.config import settings
from app.core.celery_app import celery_app

router = APIRouter(prefix="/search", tags=["Search"])


def _extract_clip_blocking(video: Video, start_sec: float, duration: float, output_key: str) -> str:
    """Extract video clip synchronously - same approach as Streamlit"""
    import uuid
    import subprocess
    
    temp_video = f"/tmp/{uuid.uuid4()}_original.mp4"
    temp_clip = f"/tmp/{uuid.uuid4()}_clip.mp4"
    
    try:
        # Download original video from S3
        logger.info(f"📥 Downloading video from S3: {video.s3_key}")
        aws_service.download_from_s3(video.s3_key, temp_video)
        
        # Validate downloaded file
        if not os.path.exists(temp_video) or os.path.getsize(temp_video) == 0:
            raise ValueError(f"Downloaded video is empty or missing")
        
        logger.info(f"📥 Downloaded {os.path.getsize(temp_video)} bytes from S3")
        
        # FFmpeg command - EXACT same as Streamlit
        cmd = [
            'ffmpeg', '-y', '-i', temp_video,
            '-ss', str(start_sec), '-t', str(duration),
            '-c:v', 'libx264', '-c:a', 'aac',
            '-preset', 'fast', '-crf', '23',
            '-avoid_negative_ts', 'make_zero',
            '-loglevel', 'error',  # Only show errors
            temp_clip
        ]
        
        logger.info(f"🎬 Running FFmpeg: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Validate extracted clip
        if not os.path.exists(temp_clip):
            raise FileNotFoundError(f"FFmpeg did not create output file: {temp_clip}")
        
        clip_size = os.path.getsize(temp_clip)
        if clip_size == 0:
            raise ValueError(f"FFmpeg created empty file: {temp_clip}")
        
        logger.info(f"✅ Generated clip: {clip_size} bytes")
        
        # Upload to S3 cache with validation
        aws_service.upload_file_to_s3(temp_clip, output_key)
        logger.info(f"💾 Cached original clip to S3: {output_key}")
        
        # Verify upload
        if not aws_service.s3_object_exists(output_key):
            raise Exception("S3 upload verification failed")
        
        # Generate presigned URL
        clip_url = aws_service.generate_presigned_url(output_key)
        
        return clip_url
        
    except subprocess.CalledProcessError as e:
        error_msg = f"FFmpeg failed: {e.stderr}"
        logger.error(f"❌ {error_msg}")
        raise Exception(error_msg)
    except Exception as e:
        logger.error(f"❌ Clip generation failed: {str(e)}")
        raise
    finally:
        # Clean up local temp files
        if os.path.exists(temp_clip):
            os.remove(temp_clip)
        if os.path.exists(temp_video):
            os.remove(temp_video)


@router.post("", response_model=SearchResponse)
async def search_videos(
    request: SearchRequest,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Semantic search for video clips with automatic processing"""
    try:
        # Generate query embedding (same as Streamlit)
        model_str = "nova-premier" if request.model == EmbeddingModel.NOVA_PREMIER else "marengo"
        logger.info("="*80)
        logger.info(f"🔍 NEW SEARCH REQUEST")
        logger.info(f"   Query: '{request.query}'")
        logger.info(f"   Model: {model_str}")
        logger.info(f"   Top K: {request.top_k}")
        logger.info("="*80)
        
        query_embedding = aws_service.generate_text_embedding(request.query, model_str)
        
        if not query_embedding:
            logger.error("❌ Failed to generate query embedding")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate query embedding"
            )
        
        logger.info(f"✅ Generated query embedding: {len(query_embedding)} dimensions")
        logger.info(f"   First 5 values: {query_embedding[:5]}")
        
        # Search Weaviate (get more results for deduplication, like Streamlit)
        search_k = min(request.top_k * 5, 100)  # Get 5x more results for better deduplication
        results = vector_service.search(
            query_embedding=query_embedding,
            top_k=search_k,
            model_type=request.model.value
        )
        
        logger.info(f"📊 Raw search returned {len(results)} results before filtering")
        
        if len(results) > 0:
            logger.info(f"   Top result similarity: {results[0]['similarity']:.4f}")
            logger.info(f"   Top result video_id: {results[0]['video_id']}")
            logger.info(f"   Top result segment_id: {results[0]['segment_id']}")
        else:
            logger.warning("⚠️ No results returned from Weaviate search!")
        
        # Filter by similarity threshold (Streamlit doesn't filter, but we keep a low threshold)
        SIMILARITY_THRESHOLD = 0.0  # Disabled to match Streamlit - show all results
        filtered_results = [r for r in results if r['similarity'] > SIMILARITY_THRESHOLD]
        
        logger.info(f"📊 Filtered {len(results)} results to {len(filtered_results)} (threshold: {SIMILARITY_THRESHOLD} - disabled)")
        
        # Limit to requested top_k (like Streamlit does)
        filtered_results = filtered_results[:request.top_k]
        logger.info(f"📊 Returning top {len(filtered_results)} results to frontend")
        
        # Format results with automatic clip extraction and processing
        search_results = []
        query_hash = hashlib.md5(request.query.encode()).hexdigest()
        
        for idx, result in enumerate(filtered_results):
            # Get video details
            video = db.query(Video).filter(Video.video_id == result['video_id']).first()
            
            if not video:
                logger.warning(f"Video not found in DB: {result['video_id']}")
                continue
            
            # Calculate optimal clip duration (10-30 seconds based on similarity)
            similarity = result['similarity']
            duration = 10.0 + (similarity * 20.0)  # 10-30 seconds
            duration = min(30.0, max(10.0, duration))
            
            # S3 keys for clips
            clip_cache_prefix = f"search-cache/{query_hash}/clips/"
            original_clip_key = f"{clip_cache_prefix}original_{result['video_id']}_{result['segment_id']}_{idx}.mp4"
            processed_clip_key = f"{clip_cache_prefix}processed_{result['video_id']}_{result['segment_id']}_{idx}.mp4"
            
            # Check for cached clips
            clip_url = None
            processed_clip_url = None
            processing_status = None
            processing_task_id = None
            
            # PRIORITY 1: Check for safety query specific clip (FASTEST!)
            query_hash_short = request.query.replace(' ', '_')[:30]
            safety_clip_key = f"clips/{result['video_id']}/query_{query_hash_short}_seg_{result['segment_id']}.mp4"
            actual_clip_key = None  # Track which clip we're actually using

            if aws_service.s3_object_exists(safety_clip_key):
                clip_url = aws_service.generate_presigned_url(safety_clip_key)
                actual_clip_key = safety_clip_key
                logger.info(f"🎯 Using safety query clip (INSTANT): {safety_clip_key}")

            # PRIORITY 2: Check for pre-generated segment clip (instant!)
            else:
                pregenerated_clip_key = f"clips/{result['video_id']}/segment_{result['segment_id']}.mp4"

                if aws_service.s3_object_exists(pregenerated_clip_key):
                    clip_url = aws_service.generate_presigned_url(pregenerated_clip_key)
                    actual_clip_key = pregenerated_clip_key
                    logger.info(f"⚡ Using pre-generated clip (instant): {pregenerated_clip_key}")

                # PRIORITY 3: Check search cache
                elif aws_service.s3_object_exists(original_clip_key):
                    clip_url = aws_service.generate_presigned_url(original_clip_key)
                    actual_clip_key = original_clip_key
                    logger.info(f"✅ Using cached clip: {original_clip_key}")

                # PRIORITY 4: Extract on-demand (fallback for old videos)
                else:
                    try:
                        logger.info(f"🔄 Generating clip on-demand (fallback)")
                        clip_url = _extract_clip_blocking(
                            video,
                            result['start_sec'],
                            duration,
                            original_clip_key
                        )
                        actual_clip_key = original_clip_key
                        logger.info(f"✅ Extracted and cached clip: {original_clip_key}")
                    except Exception as e:
                        logger.error(f"❌ Failed to extract clip: {e}")
                        # Skip this result if clip extraction fails
                        continue
            
            # Check processed clip
            if aws_service.s3_object_exists(processed_clip_key):
                processed_clip_url = aws_service.generate_presigned_url(processed_clip_key)
                processing_status = "cached"
                logger.info(f"Using cached processed clip: {processed_clip_key}")
            elif False:  # Disable on-the-fly processing for instant search
                # Process on-the-fly with YOLOv8 (DISABLED - too slow for search)
                try:
                    import tempfile
                    import os
                    
                    logger.info(f"Processing clip on-the-fly: {actual_clip_key}")
                    
                    # Download clip from S3
                    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_input:
                        temp_input_path = temp_input.name
                    
                    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_output:
                        temp_output_path = temp_output.name
                    
                    try:
                        aws_service.download_from_s3(actual_clip_key, temp_input_path)
                        
                        # Process with YOLOv8
                        success = video_service.process_video_with_yolov8(
                            input_path=temp_input_path,
                            output_path=temp_output_path,
                            enable_labeling=True,
                            enable_pii_blur=True,
                            blur_faces=True,
                            blur_plates=True,
                            enable_tracking=True,
                            confidence_threshold=0.5,
                            blur_kernel=35
                        )
                        
                        if success:
                            # Upload processed clip to S3 (cache it)
                            aws_service.upload_file_to_s3(temp_output_path, processed_clip_key)
                            processed_clip_url = aws_service.generate_presigned_url(processed_clip_key)
                            processing_status = "cached"
                            logger.info(f"Processed and cached clip: {processed_clip_key}")
                        else:
                            processing_status = "failed"
                            logger.error(f"Failed to process clip: {original_clip_key}")
                    finally:
                        # Clean up temp files
                        for path in [temp_input_path, temp_output_path]:
                            if os.path.exists(path):
                                os.remove(path)
                                
                except Exception as e:
                    logger.error(f"Failed to process clip on-the-fly: {e}")
                    processing_status = "failed"

            # Check for processed clip (generated during clip creation)
            if not processed_clip_url and actual_clip_key:
                processed_s3_key = actual_clip_key.replace('.mp4', '_processed.mp4')

                # Check if processed version exists
                if aws_service.s3_object_exists(processed_s3_key):
                    processed_clip_url = aws_service.generate_presigned_url(processed_s3_key)
                    processing_status = "cached"
                    logger.info(f"✅ Found pre-processed clip: {processed_s3_key}")
                else:
                    processing_status = "not_available"
                    logger.info(f"ℹ️ No processed version available for: {actual_clip_key}")
                    # Note: Processed clips are generated during video indexing
                    # If missing, they will be available after re-indexing

            # Generate basic clip info (fast) - detailed summary can be loaded on demand
            safety_report = None
            if clip_url:
                # Provide basic clip information immediately (no AI analysis for speed)
                clip_summary = f"**Video Clip Information**\n\n"
                clip_summary += f"- **Duration**: {duration:.0f} seconds\n"
                clip_summary += f"- **Start Time**: {result['start_sec']:.1f}s from original video\n"
                clip_summary += f"- **Video ID**: {result['video_id']}\n"
                clip_summary += f"- **Segment**: #{result['segment_id']}\n\n"
                clip_summary += "*Click to expand for detailed AI analysis of this clip segment.*"

                safety_report = {
                    'summary': clip_summary,
                    'model': model_str,
                    'clip_s3_key': actual_clip_key,  # Store for on-demand analysis
                    'analysis_available': True
                }
            
            search_results.append(SearchResult(
                video_id=result['video_id'],
                segment_id=result['segment_id'],
                timestamp=result['timestamp'],
                start_sec=result['start_sec'],
                duration=duration,
                similarity_score=result['similarity'],
                clip_url=clip_url,
                processed_clip_url=processed_clip_url,
                processing_status=processing_status,
                processing_task_id=processing_task_id,
                safety_report=safety_report,
                metadata={
                    's3_key': result['s3_key'],
                    'model_type': result['model_type'],
                    'distance': result['distance'],
                    'video_filename': video.filename if video else None,
                    'video_duration': video.duration,
                    'video_resolution': f"{video.width}x{video.height}" if video.width and video.height else None
                }
            ))
        
        logger.info(f"Search completed: {len(search_results)} results for query '{request.query}'")
        
        return SearchResponse(
            query=request.query,
            results=search_results,
            total=len(search_results),
            model_used=request.model
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )


@router.post("/analyze-clip")
async def analyze_clip(
    request: dict,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Generate detailed AI analysis for a specific clip on demand"""
    try:
        clip_s3_key = request.get('clip_s3_key')
        video_id = request.get('video_id')
        start_sec = request.get('start_sec', 0)
        duration = request.get('duration', 10)

        if not clip_s3_key or not video_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="clip_s3_key and video_id are required"
            )

        # Import the clip summary function
        from app.tasks.summary_tasks import generate_clip_summary

        # Generate detailed analysis
        logger.info(f"Generating on-demand clip analysis for {video_id}")
        clip_summary = generate_clip_summary(
            clip_s3_key=clip_s3_key,
            video_id=video_id,
            start_sec=start_sec,
            duration=duration
        )

        if not clip_summary:
            clip_summary = f"**Analysis Unavailable**\n\nDetailed analysis could not be generated for this clip at this time. Please try again later."

        return {
            'summary': clip_summary,
            'video_id': video_id,
            'status': 'success'
        }

    except Exception as e:
        logger.error(f"Clip analysis error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Clip analysis failed: {str(e)}"
        )





@router.post("/clip")
async def extract_clip(
    video_id: str,
    start_sec: float,
    duration: float = 10.0,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Extract video clip on-demand"""
    try:
        # Get video
        video = db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Generate cache key
        clip_id = f"{video_id}_{int(start_sec)}_{int(duration)}"
        clip_cache_key = f"clips/{clip_id}.mp4"
        
        # Check cache
        if aws_service.s3_object_exists(clip_cache_key):
            clip_url = aws_service.generate_presigned_url(clip_cache_key)
            return {
                "video_id": video_id,
                "clip_url": clip_url,
                "cached": True,
                "start_sec": start_sec,
                "duration": duration
            }
        
        # Extract clip
        temp_video = f"/tmp/{uuid.uuid4()}_original.mp4"
        temp_clip = f"/tmp/{uuid.uuid4()}_clip.mp4"
        
        # Download original
        aws_service.download_file_from_s3(video.s3_key, temp_video)
        
        # Extract clip
        success = video_service.extract_clip(
            input_path=temp_video,
            output_path=temp_clip,
            start_sec=start_sec,
            duration=duration
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to extract clip"
            )
        
        # Upload to S3
        aws_service.upload_file_to_s3(temp_clip, clip_cache_key)
        
        # Clean up
        for f in [temp_video, temp_clip]:
            if os.path.exists(f):
                os.remove(f)
        
        clip_url = aws_service.generate_presigned_url(clip_cache_key)
        
        logger.info(f"Clip extracted: {clip_id}")
        
        return {
            "video_id": video_id,
            "clip_url": clip_url,
            "cached": False,
            "start_sec": start_sec,
            "duration": duration
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Clip extraction error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Clip extraction failed: {str(e)}"
        )


@router.post("/process-clip")
async def process_clip(
    video_id: str,
    segment_id: int,
    clip_s3_key: str,
    options: ProcessingOptions,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Process video clip with YOLOv8 (async)"""
    try:
        # Generate output key
        processed_key = f"processed-clips/{video_id}_{segment_id}_processed.mp4"
        
        # Check if already processed
        if aws_service.s3_object_exists(processed_key):
            url = aws_service.generate_presigned_url(processed_key)
            return {
                "video_id": video_id,
                "segment_id": segment_id,
                "processed_url": url,
                "status": "cached"
            }
        
        # Trigger async processing
        task = process_video_clip.delay(
            video_id=video_id,
            segment_id=segment_id,
            input_s3_key=clip_s3_key,
            output_s3_key=processed_key,
            enable_labeling=options.enable_labeling,
            enable_pii_blur=options.enable_pii_blur,
            blur_faces=options.blur_faces,
            blur_plates=options.blur_plates,
            enable_tracking=options.enable_tracking,
            confidence_threshold=options.confidence_threshold,
            blur_intensity=options.blur_intensity
        )
        
        logger.info(f"Clip processing started: {video_id}_{segment_id}, task_id: {task.id}")
        
        return {
            "video_id": video_id,
            "segment_id": segment_id,
            "task_id": task.id,
            "status": "processing",
            "message": "Processing started. Check task status."
        }
        
    except Exception as e:
        logger.error(f"Clip processing error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Processing failed: {str(e)}"
        )


@router.post("/rate", response_model=RatingResponse)
async def rate_search_result(
    rating: RatingCreate,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """Rate a search result"""
    try:
        # Create rating
        query_hash = hashlib.md5(rating.query.encode()).hexdigest()[:8]
        
        db_rating = SearchRating(
            query=rating.query,
            query_hash=query_hash,
            video_id=rating.video_id,
            segment_id=rating.segment_id,
            model_type=rating.model_type,
            rating=rating.rating,
            similarity_score=rating.similarity_score,
            user_session=current_user.get('sub', 'anonymous')
        )
        
        db.add(db_rating)
        db.commit()
        db.refresh(db_rating)
        
        logger.info(f"Rating saved: {rating.video_id}_{rating.segment_id} = {rating.rating}/5")
        
        return db_rating
        
    except Exception as e:
        logger.error(f"Rating error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Rating failed: {str(e)}"
        )


@router.get("/processing-status/{task_id}")
async def get_processing_status(
    task_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Check the status of a clip processing task"""
    try:
        from celery.result import AsyncResult
        
        task_result = AsyncResult(task_id, app=celery_app)
        
        response = {
            "task_id": task_id,
            "status": task_result.state,
            "ready": task_result.ready(),
            "successful": task_result.successful() if task_result.ready() else None
        }
        
        if task_result.ready():
            if task_result.successful():
                result = task_result.result
                response["result"] = result
                
                # If processing completed, get the processed clip URL
                if result.get("status") == "success":
                    # Extract output key from task info (would need to be stored)
                    response["message"] = "Processing completed successfully"
            else:
                response["error"] = str(task_result.info)
        else:
            response["message"] = "Processing in progress"
        
        return response
        
    except Exception as e:
        logger.error(f"Error checking task status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check task status: {str(e)}"
        )


@router.get("/debug/embeddings")
async def debug_embeddings(
    current_user: dict = Depends(get_current_user)
):
    """Debug endpoint to check what's in Weaviate"""
    try:
        # Get total count
        response = vector_service.client.query.aggregate(vector_service.collection_name).with_meta_count().do()
        total_count = response.get('data', {}).get('Aggregate', {}).get(vector_service.collection_name, [{}])[0].get('meta', {}).get('count', 0)
        
        # Get sample embeddings
        sample_response = (
            vector_service.client.query
            .get(vector_service.collection_name, ["video_id", "segment_id", "model_type", "timestamp"])
            .with_limit(10)
            .do()
        )
        
        samples = sample_response.get('data', {}).get('Get', {}).get(vector_service.collection_name, [])
        
        # Group by model type
        model_counts = {}
        for sample in samples:
            model = sample.get('model_type', 'unknown')
            model_counts[model] = model_counts.get(model, 0) + 1
        
        return {
            "total_embeddings": total_count,
            "sample_embeddings": samples[:5],
            "model_distribution": model_counts,
            "collection_name": vector_service.collection_name
        }
        
    except Exception as e:
        logger.error(f"Debug error: {e}")
        return {"error": str(e)}


@router.get("/presets")
async def get_search_presets():
    """Get predefined search queries for common safety scenarios"""
    return {
        "presets": [
            {
                "id": "distracted_driving",
                "label": "🚗 Distracted Driving",
                "query": "driver using phone or distracted while driving"
            },
            {
                "id": "aggressive_driving",
                "label": "⚡ Aggressive Driving",
                "query": "aggressive driving harsh braking sudden lane changes"
            },
            {
                "id": "pedestrian_risk",
                "label": "🚶 Pedestrian Risk",
                "query": "pedestrian crossing near miss close call"
            },
            {
                "id": "traffic_violations",
                "label": "🚦 Traffic Violations",
                "query": "running red light stop sign violation"
            },
            {
                "id": "weather_hazards",
                "label": "🌧️ Weather Hazards",
                "query": "driving in rain fog poor visibility conditions"
            },
            {
                "id": "following_distance",
                "label": "🚙 Following Too Close",
                "query": "tailgating unsafe following distance"
            },
            {
                "id": "speeding",
                "label": "💨 Speeding",
                "query": "excessive speed fast driving speeding"
            },
            {
                "id": "hard_braking",
                "label": "🛑 Hard Braking",
                "query": "harsh braking sudden stop emergency brake"
            },
            {
                "id": "lane_violations",
                "label": "🔄 Lane Violations",
                "query": "improper lane change weaving crossing lanes"
            },
            {
                "id": "night_driving",
                "label": "🌙 Night Driving",
                "query": "driving at night dark conditions low visibility"
            }
        ]
    }
