from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.video import VideoStatus, EmbeddingModel


class VideoBase(BaseModel):
    filename: str
    embedding_model: Optional[EmbeddingModel] = None


class VideoCreate(VideoBase):
    pass


class VideoResponse(VideoBase):
    id: int
    video_id: str
    s3_key: str
    s3_uri: str
    status: VideoStatus
    file_size: Optional[int] = None
    duration: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    fps: Optional[float] = None
    indexed_at: Optional[datetime] = None
    summary: Optional[str] = None
    risk_score: Optional[float] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class VideoUploadResponse(BaseModel):
    video_id: str
    upload_url: str
    message: str


class SearchRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=500)
    top_k: int = Field(default=5, ge=1, le=20)
    model: EmbeddingModel = EmbeddingModel.NOVA_PREMIER


class SearchResult(BaseModel):
    video_id: str
    segment_id: int
    timestamp: str
    start_sec: float
    duration: float = 10.0
    similarity_score: float
    clip_url: Optional[str] = None
    processed_clip_url: Optional[str] = None
    processing_status: Optional[str] = None  # "cached", "processing", "failed", None
    processing_task_id: Optional[str] = None
    safety_report: Optional[dict] = None
    metadata: dict


class SearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    total: int
    model_used: EmbeddingModel


class ProcessingOptions(BaseModel):
    enable_labeling: bool = True
    enable_pii_blur: bool = True
    blur_faces: bool = True
    blur_plates: bool = True
    enable_tracking: bool = True
    confidence_threshold: float = Field(default=0.5, ge=0.3, le=0.9)
    blur_intensity: int = Field(default=35, ge=15, le=75)


class RatingCreate(BaseModel):
    query: str
    video_id: str
    segment_id: int
    rating: int = Field(..., ge=1, le=5)
    similarity_score: float
    model_type: EmbeddingModel


class RatingResponse(BaseModel):
    id: int
    query: str
    video_id: str
    rating: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class JobStatus(BaseModel):
    job_id: str
    video_id: str
    job_type: str
    status: str
    progress: float
    message: Optional[str] = None
    error: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
