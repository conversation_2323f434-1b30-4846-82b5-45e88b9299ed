"""
Workflow coordination tasks for sequential video processing
"""
from celery import Task, chord, group
from app.core.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.video import Video, VideoStatus
from loguru import logger
from datetime import datetime


class DatabaseTask(Task):
    """Base task with database session"""
    _db = None
    
    @property
    def db(self):
        if self._db is None:
            self._db = SessionLocal()
        return self._db
    
    def after_return(self, *args, **kwargs):
        if self._db is not None:
            self._db.close()
            self._db = None


@celery_app.task(bind=True, base=DatabaseTask)
def trigger_dual_embeddings(self, video_id: str, s3_key: str):
    """
    Trigger both Nova Premier and Marengo embedding generation in TRUE PARALLEL.
    Summary generation is triggered independently and doesn't wait for embeddings.
    """
    try:
        logger.info(f"🚀 Starting TRUE PARALLEL dual embedding generation for video: {video_id}")

        # Update video status
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.status = VideoStatus.PROCESSING
            self.db.commit()

        # Import embedding task
        from app.tasks.embedding_tasks import start_embedding_generation

        # Start both embedding jobs in TRUE PARALLEL (no linking)
        # Each embedding task will handle its own completion logic
        nova_result = start_embedding_generation.apply_async(
            args=[video_id, s3_key, "nova-premier"]
        )

        marengo_result = start_embedding_generation.apply_async(
            args=[video_id, s3_key, "marengo"]
        )

        # Start summary generation in parallel (independent of embeddings)
        from app.tasks.summary_tasks import generate_summary_from_video
        summary_result = generate_summary_from_video.delay(video_id, s3_key)

        logger.info(f"✅ TRUE PARALLEL workflow started for {video_id}")
        logger.info(f"Nova task: {nova_result.id}")
        logger.info(f"Marengo task: {marengo_result.id}")
        logger.info(f"🎬 Summary generation (independent): {summary_result.id}")
        logger.info(f"📊 Expected 50% faster completion due to true parallelism")

        return {
            "status": "started",
            "video_id": video_id,
            "nova_task_id": nova_result.id,
            "marengo_task_id": marengo_result.id,
            "summary_task_id": summary_result.id
        }

    except Exception as e:
        logger.error(f"Error starting dual embeddings for {video_id}: {e}")
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def on_embeddings_complete(self, results, video_id: str):
    """
    Called when both Nova and Marengo embeddings are complete.
    Triggers summary generation using Marengo embeddings.
    """
    try:
        logger.info(f"✅ Both embeddings complete for video: {video_id}")
        logger.info(f"Embedding results: {results}")
        
        # Verify both embeddings succeeded
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        # Check if both embeddings are indexed - WAIT if not ready
        if not video.nova_indexed_at or not video.marengo_indexed_at:
            logger.warning(f"⏳ Embeddings not yet ingested for {video_id}")
            logger.warning(f"Nova indexed: {video.nova_indexed_at}, Marengo indexed: {video.marengo_indexed_at}")
            logger.warning(f"⏸️  Summary will be triggered by ingest_embeddings task instead")
            # Don't trigger summary yet - let ingest_embeddings handle it
            return {
                "status": "waiting",
                "video_id": video_id,
                "message": "Waiting for embeddings to be ingested"
            }
        
        # Both embeddings are indexed - trigger summary generation AND clip pre-generation
        from app.tasks.summary_tasks import generate_summary_from_embeddings
        from app.tasks.clip_generation_tasks import generate_all_segment_clips
        
        logger.info(f"🚀 Triggering summary generation for {video_id}")
        generate_summary_from_embeddings.delay(video_id)
        
        # NEW: Trigger clip pre-generation for instant search results
        logger.info(f"🎬 Triggering clip pre-generation for {video_id}")
        generate_all_segment_clips.delay(video_id, video.s3_key)
        
        return {
            "status": "success",
            "video_id": video_id,
            "message": "Summary generation and clip pre-generation triggered"
        }
        
    except Exception as e:
        logger.error(f"Error in on_embeddings_complete for {video_id}: {e}")
        
        # Mark video as failed
        try:
            video = self.db.query(Video).filter(Video.video_id == video_id).first()
            if video:
                video.status = VideoStatus.FAILED
                video.processing_error = str(e)
                self.db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update video status: {db_error}")
        
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def on_summary_complete(self, video_id: str):
    """
    Called when summary generation is complete.
    Marks video as fully indexed.
    
    Note: Clips are now triggered after embeddings complete, not after summary.
    """
    try:
        logger.info(f"✅ Summary complete for video: {video_id}")
        
        # Check if both embeddings are also complete
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video and video.nova_indexed_at and video.marengo_indexed_at:
            # Update status to INDEXED now that everything is complete
            video.status = VideoStatus.INDEXED
            self.db.commit()
            logger.info(f"🎉 Video {video_id} fully processed and indexed!")
        
        return {
            "status": "success",
            "video_id": video_id,
            "message": "Video fully indexed"
        }

    except Exception as e:
        logger.error(f"Error in on_summary_complete for {video_id}: {e}")
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def check_video_completion(self, video_id: str):
    """
    Check if video processing is complete and update status accordingly.
    This is called by both embedding completion and summary completion.
    """
    try:
        logger.info(f"🔍 Checking completion status for video: {video_id}")

        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            logger.error(f"Video {video_id} not found")
            return

        # Check what's completed
        embeddings_complete = video.nova_indexed_at and video.marengo_indexed_at
        summary_complete = video.summary_completed_at is not None

        logger.info(f"Completion status for {video_id}:")
        logger.info(f"  Nova embedding: {'✅' if video.nova_indexed_at else '❌'}")
        logger.info(f"  Marengo embedding: {'✅' if video.marengo_indexed_at else '❌'}")
        logger.info(f"  Summary: {'✅' if summary_complete else '❌'}")

        # If both embeddings are complete, trigger ENHANCED clip pre-generation
        if embeddings_complete and video.status != VideoStatus.INDEXED:
            logger.info(f"🎬 Both embeddings complete, triggering ENHANCED clip pre-generation for {video_id}")
            from app.tasks.clip_generation_tasks import generate_all_segment_clips, generate_safety_query_clips

            # Start both clip generation tasks in parallel for maximum performance
            segment_task = generate_all_segment_clips.delay(video_id, video.s3_key)
            safety_task = generate_safety_query_clips.delay(video_id)

            logger.info(f"🚀 Parallel clip generation started:")
            logger.info(f"  Segment clips task: {segment_task.id}")
            logger.info(f"  Safety query clips task: {safety_task.id}")
            logger.info(f"📊 Expected result: 30x faster search responses")

        # If everything is complete, mark as INDEXED
        if embeddings_complete and summary_complete:
            video.status = VideoStatus.INDEXED
            self.db.commit()
            logger.info(f"🎉 Video {video_id} fully processed and indexed!")
        elif embeddings_complete or summary_complete:
            # At least one major component is done, keep as PROCESSING
            video.status = VideoStatus.PROCESSING
            self.db.commit()
            logger.info(f"⏳ Video {video_id} partially complete, continuing processing...")

        return {
            "status": "checked",
            "video_id": video_id,
            "embeddings_complete": embeddings_complete,
            "summary_complete": summary_complete,
            "final_status": video.status.value
        }
        
    except Exception as e:
        logger.error(f"Error in on_summary_complete for {video_id}: {e}")
        raise
