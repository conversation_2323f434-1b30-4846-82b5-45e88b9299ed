"""YouTube download tasks"""
import os
import shutil
import tempfile
from loguru import logger
import yt_dlp

from app.core.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.video import Video, VideoStatus, EmbeddingModel
from app.services.aws_service import aws_service
from app.core.config import settings
from app.tasks.video_tasks import process_video_metadata


@celery_app.task(bind=True)
def download_youtube_video(
    self, 
    video_id: str, 
    youtube_url: str, 
    embedding_model: str
):
    """Download YouTube video asynchronously and upload to S3 - OPTIMIZED
    
    Summary generation is always enabled for all videos.
    """
    temp_dir = None
    
    try:
        from datetime import datetime
        logger.info(f"🚀 Starting OPTIMIZED YouTube download for {video_id}: {youtube_url}")
        
        # Set upload start time immediately
        db = SessionLocal()
        video = db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.upload_started_at = datetime.utcnow()
            db.commit()
        db.close()
        
        # Create temp directory for download
        temp_dir = tempfile.mkdtemp()
        temp_path = os.path.join(temp_dir, f"{video_id}.mp4")
        
        # OPTIMIZATION 1: Download with better quality/speed balance
        ydl_opts = {
            'format': 'best[height<=1080][ext=mp4]/best[ext=mp4]/best',  # Max 1080p for speed
            'outtmpl': temp_path,
            'quiet': True,  # Less logging overhead
            'no_warnings': True,
            'concurrent_fragment_downloads': 4,  # Parallel fragment download
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(youtube_url, download=True)
            filename = f"{info.get('title', 'youtube_video')[:50]}.mp4"
            duration = info.get('duration', 0)
            width = info.get('width', 0)
            height = info.get('height', 0)
            fps = info.get('fps', 30)
            
            # Get the actual downloaded file path
            downloaded_file = ydl.prepare_filename(info)
            if os.path.exists(downloaded_file):
                temp_path = downloaded_file
        
        if not os.path.exists(temp_path):
            raise Exception(f"Download failed - file not found: {temp_path}")
            
        file_size = os.path.getsize(temp_path)
        logger.info(f"✅ Downloaded {file_size / 1024 / 1024:.1f}MB in {duration}s video")
        
        # OPTIMIZATION 2: Use multipart upload for large files (>100MB)
        s3_key = f"videos/{video_id}/{filename}"
        logger.info(f"⬆️  Uploading to S3: {s3_key}")
        
        if file_size > 100 * 1024 * 1024:  # 100MB
            # Multipart upload for large files
            from app.services.video_service import video_service
            video_service.upload_large_file_to_s3(temp_path, s3_key)
        else:
            # Regular upload for small files
            with open(temp_path, 'rb') as f:
                aws_service.s3_client.upload_fileobj(
                    f,
                    settings.S3_BUCKET,
                    s3_key,
                    ExtraArgs={'ContentType': 'video/mp4'}
                )
        
        logger.info(f"✅ Uploaded to S3: {s3_key}")
        
        # OPTIMIZATION 3: Update database with metadata immediately (no re-processing needed)
        from datetime import datetime
        db = SessionLocal()
        video = db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.filename = filename
            video.s3_key = s3_key
            video.s3_uri = f"s3://{settings.S3_BUCKET}/{s3_key}"
            video.file_size = file_size
            video.duration = duration
            video.width = width
            video.height = height
            video.fps = fps
            video.status = VideoStatus.UPLOADED
            video.upload_completed_at = datetime.utcnow()  # Set completion time
            db.commit()
            logger.info(f"✅ Updated database with metadata for {video_id}")
        
        db.close()
        
        # NEW WORKFLOW: Sequential processing with dual embeddings
        from app.tasks.workflow_tasks import trigger_dual_embeddings
        
        # Trigger dual embedding workflow (Nova + Marengo)
        # This will automatically trigger summary after both embeddings complete
        # Object detection is now done at search-time, not during upload
        trigger_dual_embeddings.delay(video_id, s3_key)
        logger.info(f"🚀 Triggered dual embedding workflow (Nova Premier + Marengo)")
        logger.info(f"📊 Workflow: Embeddings → Summary → Indexed")
        logger.info(f"⚠️  Object detection moved to search-time")
        
        # Cleanup temp file immediately
        if os.path.exists(temp_path):
            os.remove(temp_path)
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        logger.info(f"✅ YouTube download OPTIMIZED completed for {video_id}")
        return {"status": "success", "video_id": video_id, "file_size": file_size}
        
    except Exception as e:
        logger.error(f"❌ YouTube download failed for {video_id}: {e}")
        
        # Update database to failed status
        try:
            db = SessionLocal()
            video = db.query(Video).filter(Video.video_id == video_id).first()
            if video:
                video.status = VideoStatus.FAILED
                video.processing_error = f"YouTube download failed: {str(e)}"
                db.commit()
            db.close()
        except Exception as db_error:
            logger.error(f"Failed to update database: {db_error}")
        
        # Cleanup temp directory
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        raise
