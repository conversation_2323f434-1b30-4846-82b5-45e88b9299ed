from celery import Task
from app.core.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.video import Video, VideoStatus, ProcessingJob, EmbeddingModel
from app.services.aws_service import aws_service
from app.services.vector_service import vector_service
from loguru import logger
from datetime import datetime
import json


class DatabaseTask(Task):
    """Base task with database session"""
    _db = None
    
    @property
    def db(self):
        if self._db is None:
            self._db = SessionLocal()
        return self._db
    
    def after_return(self, *args, **kwargs):
        if self._db is not None:
            self._db.close()
            self._db = None


@celery_app.task(bind=True, base=DatabaseTask)
def start_embedding_generation(self, video_id: str, s3_key: str, model: str = "marengo"):
    """Start async embedding generation job with AWS Bedrock"""
    try:
        from datetime import datetime
        logger.info(f"Starting embedding generation for video: {video_id}, model: {model}")
        
        # Record start time (model-specific)
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            if model == "nova-premier":
                video.nova_embedding_started_at = datetime.utcnow()
            elif model == "marengo":
                video.marengo_embedding_started_at = datetime.utcnow()
            self.db.commit()
        
        # Step 1: Starting job (30%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 1,
                'total': 3,
                'percent': 30,
                'status': 'Starting AWS Bedrock embedding job...',
                'video_id': video_id
            }
        )
        
        # Broadcast WebSocket notification for job start
        try:
            import asyncio
            from app.api.routes.websocket import notify_job_update
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(
                notify_job_update(
                    job_id=f"emb_{video_id}_{model}",
                    video_id=video_id,
                    status="running",
                    progress=30.0,
                    message="Starting AWS Bedrock embedding job..."
                )
            )
            loop.close()
        except Exception as ws_error:
            logger.warning(f"Failed to send WebSocket notification: {ws_error}")
        
        # Start AWS Bedrock async job
        invocation_arn = aws_service.start_async_embedding_job(video_id, s3_key, model)
        
        if invocation_arn:
            # Step 2: Creating job record (60%)
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': 2,
                    'total': 3,
                    'percent': 60,
                    'status': 'Creating job record...',
                    'video_id': video_id
                }
            )
            
            # Create processing job record
            job = ProcessingJob(
                job_id=f"emb_{video_id}_{model}",
                video_id=video_id,
                job_type="embedding",
                status="running",
                invocation_arn=invocation_arn,
                message="AWS Bedrock embedding generation in progress"
            )
            self.db.add(job)
            
            # Update video status
            video = self.db.query(Video).filter(Video.video_id == video_id).first()
            if video:
                video.status = VideoStatus.PROCESSING
            
            self.db.commit()
            
            # Step 3: Job submitted (100%)
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': 3,
                    'total': 3,
                    'percent': 100,
                    'status': 'Embedding job submitted (processing in background)...',
                    'video_id': video_id,
                    'note': 'Embeddings will be generated in 2-5 minutes'
                }
            )
            
            logger.info(f"✅ Embedding job started with ARN: {invocation_arn}")
            logger.info(f"📊 Expected processing time: 2-5 minutes")
            logger.info(f"📁 Output will be at: s3://{aws_service.s3_client._client_config.__dict__.get('region_name', 'us-east-1')}/embeddings-output/{video_id}/output.json")
            
            return {"status": "success", "invocation_arn": invocation_arn, "percent": 100}
        else:
            raise Exception("Failed to start AWS Bedrock embedding job - check logs for details")
            
    except Exception as e:
        logger.error(f"Error starting embedding generation: {e}")
        
        # Mark video as failed
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.status = VideoStatus.FAILED
            video.processing_error = str(e)
            self.db.commit()
        
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def check_bedrock_jobs(self):
    """Periodic task to check status of Bedrock embedding jobs"""
    try:
        # Get all running embedding jobs
        jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.job_type == "embedding",
            ProcessingJob.status == "running"
        ).all()
        
        for job in jobs:
            try:
                # Check if embeddings are available in S3
                video_id = job.video_id
                
                # AWS Bedrock puts output in a subfolder with the job ID
                # Try to find output.json in any subfolder
                embeddings_found = False
                output_key = None
                
                try:
                    # List all objects in the embeddings-output folder
                    response = aws_service.s3_client.list_objects_v2(
                        Bucket='poc-video-search-bucket',
                        Prefix=f'embeddings-output/{video_id}/'
                    )
                    
                    if 'Contents' in response:
                        for obj in response['Contents']:
                            # Look for output.json in any subfolder
                            if obj['Key'].endswith('output.json'):
                                output_key = obj['Key']
                                embeddings_found = True
                                logger.info(f"Found embeddings: {output_key}")
                                ingest_embeddings.delay(video_id, output_key, job.job_id)
                                break
                except Exception as e:
                    logger.error(f"Error listing S3: {e}")
                
                if embeddings_found:
                    job.status = "completed"
                    job.completed_at = datetime.utcnow()
                    job.message = "Embeddings ready for ingestion"
                    self.db.commit()
                    logger.info(f"Embeddings found for job: {job.job_id}")
                    
            except Exception as e:
                logger.error(f"Error checking job {job.job_id}: {e}")
                continue
        
        return {"checked": len(jobs)}
        
    except Exception as e:
        logger.error(f"Error in check_bedrock_jobs: {e}")
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def ingest_embeddings(self, video_id: str, embeddings_s3_key: str, job_id: str):
    """Ingest embeddings from S3 to Weaviate"""
    try:
        logger.info(f"Ingesting embeddings for video: {video_id}")
        
        # Download embeddings from S3
        embeddings_data = aws_service.download_json_from_s3(embeddings_s3_key)
        
        if 'data' not in embeddings_data:
            raise Exception("Invalid embeddings data format")
        
        # Get video info
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            raise Exception(f"Video not found: {video_id}")
        
        # Prepare embeddings for Weaviate
        weaviate_data = []
        for i, item in enumerate(embeddings_data['data']):
            embedding = item.get('embedding', [])
            start_sec = item.get('startSec', 0)
            
            if not embedding:
                continue
            
            # Convert timestamp
            hours = int(start_sec // 3600)
            minutes = int((start_sec % 3600) // 60)
            secs = start_sec % 60
            timestamp = f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
            
            # Determine model type from job_id
            model_type = 'nova-premier' if 'nova' in job_id.lower() else 'marengo'
            
            weaviate_data.append({
                'video_id': video_id,
                'segment_id': i,
                'timestamp': timestamp,
                'start_sec': start_sec,
                'model_type': model_type,
                's3_key': video.s3_key,
                'upload_time': str(datetime.utcnow()),
                'embedding': embedding
            })
        
        # Add to Weaviate
        if weaviate_data:
            vector_service.add_embeddings(weaviate_data)
            
            # Update video status (model-specific)
            job = self.db.query(ProcessingJob).filter(ProcessingJob.job_id == job_id).first()
            if job:
                # Determine which model based on job_id
                if "nova" in job.job_id:
                    video.nova_embedding_completed_at = datetime.utcnow()
                    video.nova_indexed_at = datetime.utcnow()
                    video.nova_embedding_arn = job.invocation_arn
                elif "marengo" in job.job_id:
                    video.marengo_embedding_completed_at = datetime.utcnow()
                    video.marengo_indexed_at = datetime.utcnow()
                    logger.info(f"✅ Marengo embeddings indexed for {video_id}")
                
                # IMPORTANT: Commit BEFORE checking if both are complete to avoid race condition
                self.db.commit()
                
                # Refresh and check completion status
                self.db.refresh(video)

                # Trigger workflow completion check (handles clip generation and final status)
                from app.tasks.workflow_tasks import check_video_completion
                check_video_completion.delay(video_id)

                if video.nova_indexed_at and video.marengo_indexed_at:
                    logger.info(f"🎉 Both embeddings ingested for {video_id}!")
                else:
                    logger.info(f"⏳ Waiting for other embedding to complete for {video_id}")
                    logger.info(f"Nova: {video.nova_indexed_at}, Marengo: {video.marengo_indexed_at}")

                video.status = VideoStatus.PROCESSING
                
                # Update job status
                job.status = "completed"
                job.completed_at = datetime.utcnow()
                job.message = f"Ingested {len(weaviate_data)} embeddings"
            
            self.db.commit()
            
            # Broadcast WebSocket notification
            try:
                import asyncio
                from app.api.routes.websocket import notify_video_update
                
                # Run async function in sync context
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    notify_video_update(
                        video_id=video_id,
                        status="indexed",
                        message=f"Video indexed with {len(weaviate_data)} segments"
                    )
                )
                loop.close()
                logger.info(f"✅ WebSocket notification sent for video {video_id}")
            except Exception as ws_error:
                logger.warning(f"Failed to send WebSocket notification: {ws_error}")
                # Don't fail the task if WebSocket notification fails
            
            logger.info(f"Ingested {len(weaviate_data)} embeddings for video: {video_id}")
            return {"status": "success", "count": len(weaviate_data)}
        else:
            raise Exception("No valid embeddings to ingest")
            
    except Exception as e:
        logger.error(f"Error ingesting embeddings: {e}")
        
        # Update job status to failed
        job = self.db.query(ProcessingJob).filter(ProcessingJob.job_id == job_id).first()
        if job:
            job.status = "failed"
            job.error = str(e)
            self.db.commit()
        
        raise
