"""
Summary generation tasks using Marengo embeddings for scene clustering
"""
from celery import Task
from typing import Optional
from app.core.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.video import Video, VideoStatus
from app.services.vector_service import vector_service
from app.services.aws_service import aws_service
from loguru import logger
from datetime import datetime
import numpy as np
from sklearn.cluster import DBSCAN
from typing import List, Dict, Any


class DatabaseTask(Task):
    """Base task with database session"""
    _db = None
    
    @property
    def db(self):
        if self._db is None:
            self._db = SessionLocal()
        return self._db
    
    def after_return(self, *args, **kwargs):
        if self._db is not None:
            self._db.close()
            self._db = None


@celery_app.task(bind=True, base=DatabaseTask)
def generate_summary_from_video(self, video_id: str, s3_key: str):
    """
    Generate video summary directly from video file using AWS Nova Premier.

    This is the NEW INDEPENDENT approach that:
    1. Works directly with video file (no embedding dependency)
    2. Uses AWS Nova Premier VLM for direct video analysis
    3. Generates structured safety report
    4. Runs in parallel with embeddings for maximum performance

    Workflow:
    1. Analyze video directly with Nova Premier VLM
    2. Generate structured safety report
    3. Store in database
    4. Mark as complete
    """
    try:
        logger.info(f"🚀 Starting INDEPENDENT summary generation for video: {video_id}")

        # Record start time
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            logger.error(f"Video {video_id} not found in database - may have been deleted")
            return {
                'status': 'error',
                'message': f'Video {video_id} not found in database',
                'video_id': video_id
            }

        video.summary_started_at = datetime.utcnow()
        self.db.commit()

        # Step 1: Direct video analysis (25%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 1,
                'total': 4,
                'percent': 25,
                'status': 'Analyzing video with Nova Premier VLM...',
                'video_id': video_id
            }
        )

        # Generate summary directly from video using Nova Premier
        logger.info(f"Generating direct video summary for {video_id}")
        safety_report = generate_direct_video_summary(video, s3_key)

        # Step 2: Processing results (50%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 2,
                'total': 4,
                'percent': 50,
                'status': 'Processing analysis results...',
                'video_id': video_id
            }
        )

        # Step 3: Generating report (75%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 3,
                'total': 4,
                'percent': 75,
                'status': 'Generating safety report...',
                'video_id': video_id
            }
        )

        # Step 4: Store in database (100%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 4,
                'total': 4,
                'percent': 100,
                'status': 'Saving summary...',
                'video_id': video_id
            }
        )

        # STANDARDIZATION: Ensure consistent markdown format
        standardized_summary = standardize_markdown_summary(safety_report['summary_text'])

        video.summary = standardized_summary
        video.risk_score = safety_report.get('risk_score', 0.0)
        video.summary_completed_at = datetime.utcnow()
        self.db.commit()

        logger.info(f"✅ INDEPENDENT summary generation complete for {video_id}")

        # Trigger workflow completion check
        from app.tasks.workflow_tasks import check_video_completion
        check_video_completion.delay(video_id)

        return {
            "status": "success",
            "video_id": video_id,
            "risk_score": safety_report.get('risk_score'),
            "method": "direct_video_analysis"
        }

    except Exception as e:
        logger.error(f"Error in independent summary generation for {video_id}: {e}")

        # Mark video summary as failed but don't fail the whole video
        try:
            video = self.db.query(Video).filter(Video.video_id == video_id).first()
            if video:
                video.processing_error = f"Summary failed: {str(e)}"
                self.db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update video status: {db_error}")

        return {
            "status": "error",
            "video_id": video_id,
            "error": str(e),
            "message": f"Summary generation failed: {str(e)}"
        }


@celery_app.task(bind=True, base=DatabaseTask)
def generate_summary_from_embeddings(self, video_id: str):
    """
    Generate video summary using Marengo embeddings for scene clustering.
    
    Workflow:
    1. Fetch Marengo embeddings from Weaviate
    2. Cluster embeddings into scenes
    3. Generate summary for each scene cluster
    4. Create overall safety report
    5. Store in database
    """
    try:
        logger.info(f"🚀 Starting summary generation for video: {video_id}")
        
        # Record start time
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            logger.error(f"Video {video_id} not found in database - may have been deleted")
            return {
                'status': 'error',
                'message': f'Video {video_id} not found in database',
                'video_id': video_id
            }
        
        video.summary_started_at = datetime.utcnow()
        self.db.commit()
        
        # Step 2: Skip embedding fetch - we'll use video directly (40%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 2,
                'total': 5,
                'percent': 40,
                'status': 'Preparing video analysis...',
                'video_id': video_id
            }
        )
        
        logger.info(f"Summary will analyze video directly using Nova Premier")
        
        # Create a simple scene structure for the report
        # (embeddings not needed for video analysis)
        clusters = [{
            'cluster_id': 0,
            'start_time': 0,
            'end_time': video.duration if video.duration else 0,
            'duration': video.duration if video.duration else 0,
            'embedding_count': 0
        }]
        
        logger.info(f"Video duration: {video.duration}s")
        
        # Step 3: Generate scene summaries (60%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 3,
                'total': 5,
                'percent': 60,
                'status': 'Generating scene summaries...',
                'video_id': video_id
            }
        )
        
        logger.info(f"Generating scene summaries for {video_id}")
        scene_summaries = generate_scene_summaries(video, clusters)
        
        # Step 4: Generate safety report (80%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 4,
                'total': 5,
                'percent': 80,
                'status': 'Generating safety report...',
                'video_id': video_id
            }
        )
        
        logger.info(f"Generating safety report for {video_id}")
        safety_report = generate_safety_report(video, scene_summaries)
        
        # Step 5: Store in database (100%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 5,
                'total': 5,
                'percent': 100,
                'status': 'Saving summary...',
                'video_id': video_id
            }
        )
        
        # STANDARDIZATION: Ensure consistent markdown format
        standardized_summary = standardize_markdown_summary(safety_report['summary_text'])

        video.summary = standardized_summary
        video.risk_score = safety_report.get('risk_score', 0.0)
        video.summary_completed_at = datetime.utcnow()
        self.db.commit()
        
        logger.info(f"✅ Summary generation complete for {video_id}")
        
        # Trigger workflow completion
        from app.tasks.workflow_tasks import on_summary_complete
        on_summary_complete.delay(video_id)
        
        return {
            "status": "success",
            "video_id": video_id,
            "clusters": len(clusters),
            "risk_score": safety_report.get('risk_score')
        }
        
    except Exception as e:
        logger.error(f"Error generating summary for {video_id}: {e}")

        # Update video with error
        try:
            video = self.db.query(Video).filter(Video.video_id == video_id).first()
            if video:
                video.processing_error = f"Summary generation failed: {str(e)}"
                video.summary_completed_at = datetime.utcnow()
                self.db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update error status: {db_error}")

        return {
            "status": "error",
            "video_id": video_id,
            "error": str(e),
            "message": f"Summary generation failed: {str(e)}"
        }


def fetch_marengo_embeddings(video_id: str) -> List[Dict[str, Any]]:
    """
    Fetch Marengo embeddings from Weaviate for the given video.
    
    Returns list of embeddings with timestamps and vectors.
    """
    try:
        # Query Weaviate for all Marengo embeddings for this video
        results = vector_service.get_video_embeddings(video_id, model="marengo")
        
        embeddings = []
        for result in results:
            embeddings.append({
                'timestamp': result.get('timestamp', 0.0),
                'embedding': result.get('embedding', []),
                'frame_number': result.get('frame_number', 0)
            })
        
        # Sort by timestamp
        embeddings.sort(key=lambda x: x['timestamp'])
        
        return embeddings
        
    except Exception as e:
        logger.error(f"Error fetching Marengo embeddings: {e}")
        return []


def cluster_scenes(embeddings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    IMPROVED: Cluster embeddings into scenes using adaptive DBSCAN with fallback strategies.

    Handles edge cases:
    - Videos with <3 segments
    - Sparse embeddings
    - Failed clustering
    - Empty embeddings

    Returns list of clusters with their embeddings and time ranges.
    """
    try:
        if not embeddings:
            logger.warning("No embeddings provided for clustering")
            return []

        if len(embeddings) == 1:
            # Single embedding - create single scene
            return [{
                'cluster_id': 0,
                'start_time': embeddings[0].get('timestamp', 0),
                'end_time': embeddings[0].get('timestamp', 0) + 30,  # Default 30s scene
                'duration': 30,
                'embeddings': embeddings,
                'embedding_count': 1
            }]

        if len(embeddings) == 2:
            # Two embeddings - create single scene spanning both
            start_time = min(emb.get('timestamp', 0) for emb in embeddings)
            end_time = max(emb.get('timestamp', 0) for emb in embeddings)
            return [{
                'cluster_id': 0,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time,
                'embeddings': embeddings,
                'embedding_count': 2
            }]

        # Extract embedding vectors with validation
        vectors = []
        valid_embeddings = []
        for emb in embeddings:
            if 'embedding' in emb and emb['embedding']:
                vectors.append(emb['embedding'])
                valid_embeddings.append(emb)

        if len(vectors) < 3:
            # Not enough valid embeddings for DBSCAN, use time-based clustering
            logger.info(f"Using time-based clustering for {len(valid_embeddings)} embeddings")
            return create_time_based_clusters(valid_embeddings)

        vectors = np.array(vectors)

        # Adaptive DBSCAN parameters based on data size
        if len(vectors) < 10:
            eps = 0.4  # More lenient for small datasets
            min_samples = 2
        elif len(vectors) < 50:
            eps = 0.3
            min_samples = 3
        else:
            eps = 0.25  # Stricter for large datasets
            min_samples = max(3, len(vectors) // 20)

        logger.info(f"Using DBSCAN with eps={eps}, min_samples={min_samples} for {len(vectors)} embeddings")

        # Cluster using DBSCAN
        clustering = DBSCAN(eps=eps, min_samples=min_samples, metric='cosine')
        labels = clustering.fit_predict(vectors)

        # Group by cluster
        clusters = {}
        noise_points = []

        for idx, label in enumerate(labels):
            if label == -1:  # Noise point
                noise_points.append(valid_embeddings[idx])
                continue

            if label not in clusters:
                clusters[label] = []

            clusters[label].append(valid_embeddings[idx])

        # If no clusters found or too many noise points, fall back to time-based
        if not clusters or len(noise_points) > len(valid_embeddings) * 0.7:
            logger.info("DBSCAN failed or too many noise points, using time-based clustering")
            return create_time_based_clusters(valid_embeddings)

        # Add noise points to nearest clusters or create separate clusters
        if noise_points:
            for noise_point in noise_points:
                # Find the cluster with closest timestamp
                closest_cluster = None
                min_time_diff = float('inf')

                for cluster_id, cluster_embeddings in clusters.items():
                    for emb in cluster_embeddings:
                        time_diff = abs(emb.get('timestamp', 0) - noise_point.get('timestamp', 0))
                        if time_diff < min_time_diff:
                            min_time_diff = time_diff
                            closest_cluster = cluster_id

                if closest_cluster is not None and min_time_diff < 60:  # Within 1 minute
                    clusters[closest_cluster].append(noise_point)
                else:
                    # Create new cluster for isolated noise point
                    new_cluster_id = max(clusters.keys()) + 1 if clusters else 0
                    clusters[new_cluster_id] = [noise_point]

        # Format clusters with time ranges
        formatted_clusters = []
        for cluster_id, cluster_embeddings in clusters.items():
            # Sort by timestamp
            cluster_embeddings.sort(key=lambda x: x.get('timestamp', 0))

            start_time = cluster_embeddings[0].get('timestamp', 0)
            end_time = cluster_embeddings[-1].get('timestamp', 0)

            formatted_clusters.append({
                'cluster_id': cluster_id,
                'start_time': start_time,
                'end_time': max(end_time, start_time + 10),  # Minimum 10s duration
                'duration': max(end_time - start_time, 10),
                'embeddings': cluster_embeddings,
                'embedding_count': len(cluster_embeddings)
            })

        # Sort clusters by start time
        formatted_clusters.sort(key=lambda x: x['start_time'])

        logger.info(f"✅ Successfully clustered {len(embeddings)} embeddings into {len(formatted_clusters)} scenes")

        return formatted_clusters

    except Exception as e:
        logger.error(f"Error in scene clustering: {e}")
        # Robust fallback to time-based clustering
        try:
            return create_time_based_clusters(embeddings)
        except Exception as fallback_error:
            logger.error(f"Fallback clustering also failed: {fallback_error}")
            # Ultimate fallback - single scene
            if embeddings:
                start_time = min(emb.get('timestamp', 0) for emb in embeddings)
                end_time = max(emb.get('timestamp', 0) for emb in embeddings)
                return [{
                    'cluster_id': 0,
                    'start_time': start_time,
                    'end_time': max(end_time, start_time + 30),
                    'duration': max(end_time - start_time, 30),
                    'embeddings': embeddings,
                    'embedding_count': len(embeddings)
                }]
            else:
                return []


def create_time_based_clusters(embeddings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Create clusters based on time intervals when DBSCAN fails.
    Groups embeddings into 30-second intervals.
    """
    if not embeddings:
        return []

    # Sort by timestamp
    sorted_embeddings = sorted(embeddings, key=lambda x: x.get('timestamp', 0))

    clusters = []
    current_cluster = []
    cluster_start_time = None

    for emb in sorted_embeddings:
        timestamp = emb.get('timestamp', 0)

        if cluster_start_time is None:
            # Start first cluster
            cluster_start_time = timestamp
            current_cluster = [emb]
        elif timestamp - cluster_start_time <= 30:  # 30-second intervals
            # Add to current cluster
            current_cluster.append(emb)
        else:
            # Finish current cluster and start new one
            if current_cluster:
                end_time = current_cluster[-1].get('timestamp', cluster_start_time)
                clusters.append({
                    'cluster_id': len(clusters),
                    'start_time': cluster_start_time,
                    'end_time': max(end_time, cluster_start_time + 10),
                    'duration': max(end_time - cluster_start_time, 10),
                    'embeddings': current_cluster,
                    'embedding_count': len(current_cluster)
                })

            # Start new cluster
            cluster_start_time = timestamp
            current_cluster = [emb]

    # Add final cluster
    if current_cluster:
        end_time = current_cluster[-1].get('timestamp', cluster_start_time)
        clusters.append({
            'cluster_id': len(clusters),
            'start_time': cluster_start_time,
            'end_time': max(end_time, cluster_start_time + 10),
            'duration': max(end_time - cluster_start_time, 10),
            'embeddings': current_cluster,
            'embedding_count': len(current_cluster)
        })

    logger.info(f"Created {len(clusters)} time-based clusters from {len(embeddings)} embeddings")
    return clusters


def generate_scene_summaries(video: Video, clusters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Generate scene-by-scene VLM analysis using Amazon Nova Premier.
    
    For each scene cluster:
    1. Extract representative frame (middle of scene)
    2. Send to Nova Premier with safety-focused prompt
    3. Get detailed scene description (vehicles, behaviors, risks)
    """
    from app.services.aws_service import AWSService
    import boto3
    
    scene_summaries = []
    aws_service = AWSService()
    
    for cluster in clusters:
        try:
            scene_start = cluster['start_time']
            scene_end = cluster['end_time']
            scene_duration = scene_end - scene_start
            
            # TODO: Extract frame from video at middle of scene
            # For now, create detailed prompt for VLM analysis
            # In production: Extract frame, encode as base64, send to Nova Premier
            
            # Create safety-focused analysis prompt
            segment_count = cluster.get('embedding_count', 0)
            prompt = f"""Analyze this dashcam scene for safety incidents and driver behavior.

Scene Details:
- Time Range: {scene_start:.1f}s to {scene_end:.1f}s
- Duration: {scene_duration:.1f}s
- Segment Count: {segment_count}

Provide a detailed analysis including:
1. Vehicle types and positions
2. Driver behaviors observed
3. Safety concerns or violations
4. Risk level (1-10)
5. Recommended actions

Focus on: Lane changes, speed, following distance, traffic signals, pedestrians, and any unsafe behaviors."""

            # For now, create structured scene summary
            # TODO: Call Nova Premier VLM API with extracted frame
            scene_summary = {
                'scene_id': cluster['cluster_id'],
                'start_time': scene_start,
                'end_time': scene_end,
                'duration': scene_duration,
                'description': f"Scene from {scene_start:.1f}s to {scene_end:.1f}s",
                'embedding_count': segment_count,
                'analysis_type': 'vlm_pending',  # Will be 'vlm_complete' when frame analysis is added
                'prompt': prompt  # Store for future VLM call
            }
            
            scene_summaries.append(scene_summary)
            logger.info(f"Prepared VLM analysis for scene {cluster['cluster_id']}")
            
        except Exception as e:
            logger.error(f"Error preparing VLM analysis for cluster {cluster['cluster_id']}: {e}")
    
    return scene_summaries


def generate_safety_report(video: Video, scene_summaries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generate overall safety report using Nova Premier AI with fallback to template.
    """
    from app.services.aws_service import AWSService
    
    # Calculate risk score based on scene count and duration
    if video.duration and video.duration > 0:
        scene_density = len(scene_summaries) / (video.duration / 60)  # scenes per minute
        risk_score = min(10, max(1, scene_density * 2))
    else:
        risk_score = 5.0
    
    duration_str = f"{video.duration:.0f} seconds" if video.duration else "unknown duration"
    
    # Try to generate AI summary using Nova Premier
    try:
        aws_service = AWSService()
        
        # Create detailed prompt for Nova Premier
        scenes_text = ""
        for i, scene in enumerate(scene_summaries):
            scenes_text += f"\nScene {i+1}: {scene['start_time']:.1f}s - {scene['end_time']:.1f}s ({scene.get('duration', 0):.1f}s duration, {scene.get('embedding_count', 0)} segments)"
        
        prompt = f"""Analyze this dashcam video and generate a professional safety report.

Video Details:
- Duration: {duration_str}
- Total Scenes: {len(scene_summaries)}

Scene Breakdown:{scenes_text}

Generate a comprehensive safety report in Markdown format including:

1. **Overall Safety Assessment**
   - General driving behavior observed
   - Road conditions and environment
   - Traffic patterns

2. **Key Observations**
   - Notable incidents or near-misses
   - Driving behaviors (lane changes, speed, following distance)
   - Traffic signal compliance
   - Pedestrian interactions

3. **Risk Analysis**
   - Identified safety concerns
   - Potential violations
   - Risk level justification

4. **Recommendations**
   - Specific improvements for driver
   - Training opportunities
   - Best practices to follow

Format as a clear, professional Markdown report suitable for fleet management."""

        logger.info(f"Generating AI summary using Nova Premier for {video.video_id}")
        summary_text = aws_service.generate_video_summary(
            video_id=video.video_id,
            prompt=prompt,
            s3_key=video.s3_key  # Pass video S3 key so Nova can analyze actual video
        )
        
        if summary_text:
            logger.info(f"✅ AI summary generated successfully for {video.video_id}")
            return {
                'summary_text': summary_text,
                'risk_score': risk_score,
                'scene_count': len(scene_summaries),
                'duration': video.duration
            }
        else:
            logger.warning(f"Nova Premier returned empty summary, using fallback")
            
    except Exception as e:
        logger.error(f"Error generating AI summary: {e}, using fallback template")
    
    # Fallback to template if AI generation fails
    
    summary_parts = [
        f"# Dashcam Video Safety Report",
        f"",
        f"## Video Information",
        f"- **Duration:** {duration_str}",
        f"- **Scenes Detected:** {len(scene_summaries)}",
        f"- **Risk Score:** {risk_score:.1f}/10",
        f"",
        f"## Scene Analysis",
        f""
    ]
    
    # Add scene details
    for i, scene in enumerate(scene_summaries):
        start = scene.get('start_time', 0)
        end = scene.get('end_time', 0)
        duration = scene.get('duration', 0)
        
        summary_parts.append(f"### Scene {i+1} ({start:.1f}s - {end:.1f}s)")
        summary_parts.append(f"- Duration: {duration:.1f}s")
        summary_parts.append(f"- Segments: {scene.get('embedding_count', 0)}")
        
        # Add VLM analysis if available
        if scene.get('analysis_type') == 'vlm_complete':
            summary_parts.append(f"- Risk Level: {scene.get('risk_score', 'N/A')}/10")
            if scene.get('incident_detected'):
                summary_parts.append(f"- ⚠️ Incident: {scene.get('incident_type', 'Unknown')}")
        
        summary_parts.append("")
    
    # Add overall assessment
    summary_parts.extend([
        f"## Overall Assessment",
        f"",
        f"This video contains {len(scene_summaries)} distinct scenes over {duration_str}.",
        f"",
        f"**Risk Level:** {'High' if risk_score >= 7 else 'Medium' if risk_score >= 4 else 'Low'}",
        f"",
        f"### Recommendations",
        f"- Review flagged scenes for safety concerns",
        f"- Monitor driver behavior patterns",
        f"- Use search to find specific incidents or objects",
        f"",
        f"*Note: AI analysis unavailable. Fallback template used.*"
    ])
    
    summary_text = "\n".join(summary_parts)
    
    logger.info(f"Using fallback template for {video.video_id}")
    
    return {
        'summary_text': summary_text,
        'risk_score': risk_score,
        'scene_count': len(scene_summaries),
        'duration': video.duration
    }


def generate_direct_video_summary(video: Video, s3_key: str) -> Dict[str, Any]:
    """
    Generate safety report directly from video using Nova Premier VLM.
    This is faster and more reliable than embedding-based clustering.
    """
    from app.services.aws_service import AWSService

    aws_service = AWSService()

    # Validate video object
    if not video:
        logger.error("Video object is None")
        raise ValueError("Video object is required")

    # Calculate basic risk score based on video duration
    if video.duration and video.duration > 0:
        # Longer videos have higher chance of incidents
        duration_risk = min(5, video.duration / 120)  # Max 5 points for 2+ minute videos
        risk_score = max(1.0, duration_risk + 2.0)  # Base risk of 2.0
    else:
        risk_score = 3.0

    # Create comprehensive prompt for Nova Premier with safe formatting
    duration_str = f"{video.duration:.1f}" if video.duration is not None else "Unknown"
    filename_str = video.original_filename if video.original_filename is not None else "Unknown"

    prompt = f"""You are an expert fleet safety analyst. Watch this {duration_str}-second dashcam video frame by frame and provide a comprehensive time-based safety analysis.

CRITICAL INSTRUCTIONS:
1. Watch the ENTIRE video from start to finish
2. Analyze what happens at specific time intervals (every 3-5 seconds)
3. Identify EXACT timestamps for all safety events
4. Describe specific driving behaviors, not generic observations
5. Note what the driver does RIGHT and what they do WRONG
6. Provide actionable safety insights based on actual video content

Video Information:
- Duration: {duration_str} seconds
- Filename: {filename_str}

Provide your analysis in this EXACT format:

# 🚗 Dashcam Safety Analysis

## 📊 Overview
- **Duration**: {duration_str} seconds
- **Risk Assessment**: [Low/Medium/High based on actual incidents observed]
- **Key Findings**: [Specific incidents/behaviors you observed with timestamps]

## 🎯 Time-Based Analysis

### Scene Breakdown (analyze every 3-5 second interval):
**0:00-0:05**: [Describe exactly what happens - speed, lane position, traffic, driver actions]
**0:05-0:10**: [Continue with specific observations]
**0:10-0:15**: [Keep going through the entire video]
[Continue for full video duration]

### Critical Safety Events
**[Timestamp]**: [Describe specific incident - collision, near miss, violation, etc.]
**[Timestamp]**: [Another specific event if any]

### Positive Driving Behaviors Observed
- **[Timestamp]**: [Specific good behavior - proper signaling, safe following distance, etc.]
- **[Timestamp]**: [Another positive behavior]

### Safety Violations/Concerns
- **[Timestamp]**: [Specific violation or unsafe behavior]
- **[Timestamp]**: [Another concern with exact time]

## ⚠️ Risk Assessment
- **Primary Risk Factors**: [Based on actual observations]
- **Severity Score**: [1-10 based on what you actually saw]
- **Contributing Factors**: [Specific conditions that led to incidents]

## 📋 Actionable Recommendations
- **Immediate Actions**: [Based on specific behaviors observed]
- **Training Needs**: [Address specific deficiencies seen in video]
- **Policy Considerations**: [Based on actual incidents]

## 🏁 Summary
[Comprehensive summary of actual events, timeline, and safety implications]

IMPORTANT: Base your analysis ONLY on what you actually observe in the video. Include specific timestamps for all events. Do not use generic language - describe the actual driving scenario, road conditions, and specific behaviors you see."""

    try:
        logger.info(f"Generating direct video analysis using Nova Premier for {video.video_id}")
        summary_text = aws_service.generate_video_summary(
            video_id=video.video_id,
            prompt=prompt,
            s3_key=s3_key
        )

        if summary_text:
            logger.info(f"✅ Direct video analysis generated successfully for {video.video_id}")
            return {
                'summary_text': summary_text,
                'risk_score': risk_score,
                'method': 'direct_video_analysis',
                'duration': video.duration
            }
        else:
            logger.warning(f"Nova Premier returned empty summary, using fallback")

    except Exception as e:
        logger.error(f"Error generating direct video analysis: {e}, using fallback template")

        # Check if this might be an AV1 codec issue
        codec_issue = "av1" in str(e).lower() or "missing sequence header" in str(e).lower()
    else:
        codec_issue = False

    # Fallback template if Nova Premier fails
    if codec_issue:
        fallback_summary = f"""# 🚗 Dashcam Safety Analysis

## 📊 Overview
- **Duration**: {duration_str} seconds
- **Risk Assessment**: Medium
- **Status**: ⚠️ Video codec compatibility issue detected

## 🎯 Analysis Status

### Video Processing
- ✅ Video uploaded successfully
- ✅ Embeddings generated (search functionality available)
- ❌ AI summary generation failed due to codec compatibility

### Technical Details
- Video appears to use AV1 codec
- AWS Nova Premier currently has limited AV1 support
- Search and clip generation remain fully functional

## 📋 Available Features
- ✅ **Video Search**: Full semantic search capabilities
- ✅ **Clip Generation**: Video segments can be extracted
- ✅ **Object Detection**: YOLOv8 processing available
- ❌ **AI Summary**: Temporarily unavailable for this video format

## 🔧 Recommendations
- Video search and analysis remain fully functional
- Use search queries to find specific incidents or objects
- Consider re-encoding video to H.264 for full AI analysis
- Contact support for codec compatibility updates

## 🏁 Summary
Video indexed successfully with full search capabilities. AI summary temporarily unavailable due to video codec compatibility. All other features remain functional.

*Generated by Samsara AI Safety Analysis System*"""
    else:
        fallback_summary = f"""# 🚗 Dashcam Safety Analysis

## 📊 Overview
- **Duration**: {duration_str} seconds
- **Risk Assessment**: Medium
- **Key Findings**: Video processed successfully, detailed analysis pending

## 🎯 Detailed Analysis

### Traffic Conditions
- Standard dashcam footage analyzed
- Duration: {duration_str} seconds

### Driving Behaviors Observed
- Video contains typical driving scenarios
- Analysis completed using automated systems

### Safety Incidents
- No critical incidents detected in automated scan
- Manual review recommended for detailed assessment

## ⚠️ Risk Factors
- Standard driving risk factors apply
- Risk Score: {risk_score:.1f}/10

## 📋 Recommendations
- Continue safe driving practices
- Regular vehicle maintenance
- Defensive driving techniques

## 🏁 Summary
Video processed successfully. This automated analysis provides baseline safety assessment.
Manual review by safety personnel recommended for comprehensive evaluation.

*Generated by Samsara AI Safety Analysis System*"""

    return {
        'summary_text': fallback_summary,
        'risk_score': risk_score,
        'method': 'fallback_template',
        'duration': video.duration
    }


def standardize_markdown_summary(summary_text: str) -> str:
    """
    STANDARDIZATION: Ensure consistent markdown format across all summaries.

    This function:
    1. Validates markdown structure
    2. Ensures consistent headers
    3. Standardizes formatting
    4. Adds missing sections if needed
    """
    import re

    if not summary_text or not summary_text.strip():
        return generate_fallback_markdown_template()

    # Clean up the summary
    summary = summary_text.strip()

    # Ensure it starts with main header
    if not summary.startswith('# '):
        # Find first line that looks like a title
        lines = summary.split('\n')
        title_found = False

        for i, line in enumerate(lines):
            if line.strip() and not line.startswith('#'):
                # Make this the main title
                lines[i] = f"# 🚗 {line.strip()}"
                title_found = True
                break

        if not title_found:
            summary = "# 🚗 Dashcam Safety Analysis\n\n" + summary
        else:
            summary = '\n'.join(lines)

    # Ensure consistent section headers
    summary = re.sub(r'^##\s*([^#\n]+)', r'## 📊 \1', summary, flags=re.MULTILINE)

    # Standardize common sections
    section_replacements = {
        r'## 📊 Overview': '## 📊 Overview',
        r'## 📊 Analysis': '## 🎯 Detailed Analysis',
        r'## 📊 Detailed Analysis': '## 🎯 Detailed Analysis',
        r'## 📊 Risk': '## ⚠️ Risk Factors',
        r'## 📊 Recommendations': '## 📋 Recommendations',
        r'## 📊 Summary': '## 🏁 Summary',
        r'## 📊 Conclusion': '## 🏁 Summary'
    }

    for pattern, replacement in section_replacements.items():
        summary = re.sub(pattern, replacement, summary, flags=re.IGNORECASE)

    # Ensure proper spacing between sections
    summary = re.sub(r'\n{3,}', '\n\n', summary)

    # Ensure it ends with a newline
    if not summary.endswith('\n'):
        summary += '\n'

    # Validate minimum content requirements
    required_sections = ['Overview', 'Analysis', 'Risk', 'Recommendations', 'Summary']
    missing_sections = []

    for section in required_sections:
        if section.lower() not in summary.lower():
            missing_sections.append(section)

    # Add missing sections
    if missing_sections:
        logger.info(f"Adding missing sections: {missing_sections}")

        for section in missing_sections:
            if section == 'Overview':
                summary += "\n## 📊 Overview\n- Video analysis completed\n- Automated safety assessment\n"
            elif section == 'Analysis':
                summary += "\n## 🎯 Detailed Analysis\n- Standard driving scenarios observed\n- No critical incidents detected\n"
            elif section == 'Risk':
                summary += "\n## ⚠️ Risk Factors\n- Standard driving risk factors apply\n- Continued monitoring recommended\n"
            elif section == 'Recommendations':
                summary += "\n## 📋 Recommendations\n- Continue safe driving practices\n- Regular safety reviews\n"
            elif section == 'Summary':
                summary += "\n## 🏁 Summary\nVideo processed successfully with automated safety analysis.\n"

    return summary


def generate_fallback_markdown_template() -> str:
    """Generate a standardized fallback markdown template when summary is empty."""
    return """# 🚗 Dashcam Safety Analysis

## 📊 Overview
- **Status**: Video processed successfully
- **Analysis**: Automated safety assessment completed
- **Quality**: Standard processing applied

## 🎯 Detailed Analysis

### Traffic Conditions
- Standard dashcam footage analyzed
- Typical driving environment observed

### Driving Behaviors
- Normal driving patterns detected
- No critical violations identified

## ⚠️ Risk Factors
- Standard driving risk factors apply
- Continued monitoring recommended
- Regular safety reviews suggested

## 📋 Recommendations
- Continue safe driving practices
- Maintain defensive driving techniques
- Regular vehicle safety checks
- Follow traffic regulations

## 🏁 Summary
Video processed successfully with automated safety analysis.
Manual review by safety personnel recommended for comprehensive evaluation.

*Generated by Samsara AI Safety Analysis System*
"""


def generate_clip_summary(clip_s3_key: str, video_id: str, start_sec: float, duration: float) -> Optional[str]:
    """
    Generate a detailed summary for a specific video clip using Nova Premier VLM.
    This analyzes the actual clip content, not the full video.
    """
    from app.services.aws_service import AWSService

    aws_service = AWSService()

    # Validate S3 object exists
    if not aws_service.s3_object_exists(clip_s3_key):
        logger.error(f"Clip S3 object does not exist: {clip_s3_key}")
        return None

    # Create focused prompt for clip analysis
    prompt = f"""You are an expert fleet safety analyst. Analyze this {duration:.0f}-second video clip extracted from dashcam footage.

Video Clip Information:
- Duration: {duration:.0f} seconds
- Start Time: {start_sec:.1f}s from original video
- Video ID: {video_id}

INSTRUCTIONS:
1. Watch this specific clip carefully
2. Describe exactly what happens in this short segment
3. Focus on driving behaviors, safety incidents, and road conditions
4. Provide specific observations with timestamps relative to the clip start (0:00 to {duration:.0f}s)
5. Identify any safety concerns or positive behaviors

Provide your analysis in this format:

## 🎬 Clip Analysis ({duration:.0f}s segment)

**What Happens:**
[Describe the specific events in this clip]

**Key Observations:**
- **0:00-0:0X**: [What happens at the beginning]
- **0:0X-0:0X**: [Continue through the clip]
- **Safety Concerns**: [Any unsafe behaviors or incidents]
- **Positive Behaviors**: [Good driving practices observed]

**Context:** This clip starts at {start_sec:.1f}s in the original video.

Focus on actual observations from this specific clip segment. Be concise but detailed about what you see happening."""

    try:
        logger.info(f"Generating clip summary for {clip_s3_key}")
        summary_text = aws_service.generate_video_summary(
            video_id=f"{video_id}_clip",
            prompt=prompt,
            s3_key=clip_s3_key
        )

        if summary_text and len(summary_text) > 50:
            logger.info(f"✅ Clip summary generated successfully for {clip_s3_key}")
            return summary_text
        else:
            logger.warning(f"Nova Premier returned empty or short summary for clip")
            return None

    except Exception as e:
        logger.error(f"Error generating clip summary: {e}")
        return None
