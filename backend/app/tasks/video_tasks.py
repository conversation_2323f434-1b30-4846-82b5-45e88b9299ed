from celery import Task
from app.core.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.video import Video, VideoStatus, ProcessingJob
from app.services.aws_service import aws_service
from app.services.video_service import video_service
from loguru import logger
import os
import uuid


class DatabaseTask(Task):
    """Base task with database session"""
    _db = None
    
    @property
    def db(self):
        if self._db is None:
            self._db = SessionLocal()
        return self._db
    
    def after_return(self, *args, **kwargs):
        if self._db is not None:
            self._db.close()
            self._db = None


@celery_app.task(bind=True, base=DatabaseTask)
def process_video_metadata(self, video_id: str, s3_key: str):
    """Process video metadata after upload - extract metadata and trigger processing"""
    try:
        from datetime import datetime
        logger.info(f"Processing video metadata: {video_id}")
        
        # Set processing start time
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.upload_started_at = datetime.utcnow()
            self.db.commit()
        
        # Step 1: Download from S3 to temp file for metadata extraction (30%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 1,
                'total': 3,
                'percent': 30,
                'status': 'Downloading video for metadata extraction...',
                'video_id': video_id
            }
        )
        local_path = f"/tmp/{video_id}_metadata.mp4"
        aws_service.download_from_s3(s3_key, local_path)
        logger.info(f"✓ Downloaded for metadata extraction: {video_id}")
        
        # Step 2: Extract metadata (60%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 2,
                'total': 3,
                'percent': 60,
                'status': 'Extracting video metadata...',
                'video_id': video_id
            }
        )
        metadata = video_service.get_video_metadata(local_path)
        logger.info(f"✓ Metadata extracted for {video_id}")
        
        # Step 3: Update database (80%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 3,
                'total': 3,
                'percent': 80,
                'status': 'Updating database...',
                'video_id': video_id
            }
        )
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.status = VideoStatus.UPLOADED
            video.upload_completed_at = datetime.utcnow()
            video.width = metadata.get('width')
            video.height = metadata.get('height')
            video.fps = metadata.get('fps')
            video.duration = metadata.get('duration')
            self.db.commit()
            
            # Send WebSocket notification
            try:
                import asyncio
                from app.api.routes.websocket import notify_video_update
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    notify_video_update(
                        video_id=video_id,
                        status="uploaded",
                        message="Video uploaded successfully"
                    )
                )
                loop.close()
            except Exception as ws_error:
                logger.warning(f"Failed to send WebSocket notification: {ws_error}")
        
        logger.info(f"✓ Database updated for {video_id}")
        
        # Clean up temp file
        if os.path.exists(local_path):
            os.remove(local_path)
        logger.info(f"✓ Cleaned up temp file for {video_id}")
        
        logger.info(f"✅ Video upload processed: {video_id}")
        
        # Trigger processing tasks in parallel
        logger.info(f"🚀 Triggering parallel processing tasks for {video_id}")
        
        # NOTE: This task is deprecated - new workflow uses trigger_dual_embeddings instead
        # Trigger dual embedding generation (Nova + Marengo)
        from app.tasks.workflow_tasks import trigger_dual_embeddings
        trigger_dual_embeddings.delay(video_id, s3_key)
        logger.info(f"Triggered dual embedding workflow for video: {video_id}")
        
        # Send WebSocket notification for processing start
        try:
            import asyncio
            from app.api.routes.websocket import notify_video_update
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(
                notify_video_update(
                    video_id=video_id,
                    status="processing",
                    message="Starting video processing"
                )
            )
            loop.close()
        except Exception as ws_error:
            logger.warning(f"Failed to send WebSocket notification: {ws_error}")
        
        # Trigger summary generation
        generate_video_summary.delay(video_id, s3_key)
        logger.info(f"Triggered summary generation for video: {video_id}")
        
        # Trigger object detection (YOLO)
        detect_objects_in_video.delay(video_id, s3_key)
        logger.info(f"Triggered object detection for video: {video_id}")
        
        # Record completion time
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.upload_completed_at = datetime.utcnow()
            self.db.commit()
        
        return {"status": "success", "video_id": video_id, "percent": 100}
        
    except Exception as e:
        logger.error(f"Error processing video upload: {e}")
        
        # Update status to failed
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.status = VideoStatus.FAILED
            video.processing_error = str(e)
            self.db.commit()
        
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def generate_video_summary(self, video_id: str, s3_key: str):
    """Generate video summary using Nova Premier"""
    try:
        from datetime import datetime
        logger.info(f"Generating summary for video: {video_id}")
        
        # Record start time
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.summary_started_at = datetime.utcnow()
            self.db.commit()
        
        # Step 1: Generate summary (50%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 1,
                'total': 2,
                'percent': 50,
                'status': 'Generating AI summary...',
                'video_id': video_id
            }
        )
        summary = aws_service.generate_video_summary(video_id, s3_key)
        
        if summary:
            # Step 2: Save results (100%)
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': 2,
                    'total': 2,
                    'percent': 100,
                    'status': 'Saving summary...',
                    'video_id': video_id
                }
            )
            
            # Save to S3
            summary_key = f'summaries/{video_id}/safety_report.json'
            summary_data = {
                'video_id': video_id,
                'summary': summary,
                'model': 'nova-premier'
            }
            aws_service.upload_json_to_s3(summary_data, summary_key)
            
            # Update database
            video = self.db.query(Video).filter(Video.video_id == video_id).first()
            if video:
                video.summary = summary
                video.summary_completed_at = datetime.utcnow()
                self.db.commit()
            
            # Broadcast WebSocket notification
            try:
                import asyncio
                from app.api.routes.websocket import notify_job_update
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    notify_job_update(
                        job_id=f"summary_{video_id}",
                        video_id=video_id,
                        status="completed",
                        progress=100.0,
                        message="Summary generated successfully"
                    )
                )
                loop.close()
            except Exception as ws_error:
                logger.warning(f"Failed to send WebSocket notification: {ws_error}")
            
            logger.info(f"✅ Summary generated for video: {video_id}")
            # Object detection already triggered in parallel from upload task
            return {"status": "success", "video_id": video_id}
        else:
            raise Exception("Failed to generate summary")
            
    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def detect_objects_in_video(self, video_id: str, s3_key: str):
    """Detect objects in video using YOLOv8"""
    try:
        from datetime import datetime
        logger.info(f"Starting object detection for video: {video_id}")
        
        # Set detection start time
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        detection_started_at = datetime.utcnow()
        
        # Import YOLO service
        from app.services.yolo_service import yolo_service
        
        # If local file doesn't exist, download from S3
        local_path = f"/tmp/{video_id}.mp4"
        aws_service.download_from_s3(s3_key, local_path)
        
        # Run object detection
        results = yolo_service.detect_objects_in_video(
            local_path,
            sample_rate=30,  # Process 1 frame per second at 30fps
            confidence_threshold=0.5
        )
        
        # Update database with detections
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            detection_completed_at = datetime.utcnow()
            detection_duration = (detection_completed_at - detection_started_at).total_seconds()
            
            video.object_detections = {
                'object_summary': results['object_summary'],
                'total_frames': results['total_frames'],
                'processed_frames': results['processed_frames'],
                'fps': results['fps'],
                'started_at': detection_started_at.isoformat() + 'Z',  # Add Z for UTC
                'completed_at': detection_completed_at.isoformat() + 'Z',  # Add Z for UTC
                'duration_seconds': detection_duration
            }
            self.db.commit()
        
        # Clean up temp file
        if os.path.exists(local_path) and local_path.startswith('/tmp/'):
            os.remove(local_path)
        
        # Broadcast WebSocket notification
        try:
            import asyncio
            from app.api.routes.websocket import notify_job_update
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(
                notify_job_update(
                    job_id=f"detection_{video_id}",
                    video_id=video_id,
                    status="completed",
                    progress=100.0,
                    message=f"Detected {len(results['object_summary'])} object types"
                )
            )
            loop.close()
        except Exception as ws_error:
            logger.warning(f"Failed to send WebSocket notification: {ws_error}")
        
        logger.info(f"✅ Object detection completed for video: {video_id}")
        logger.info(f"Detected objects: {results['object_summary']}")
        
        return {"status": "success", "video_id": video_id, "detections": results['object_summary']}
        
    except Exception as e:
        logger.error(f"Error detecting objects: {e}")
        
        # Update status
        video = self.db.query(Video).filter(Video.video_id == video_id).first()
        if video:
            video.processing_error = f"Object detection failed: {str(e)}"
            self.db.commit()
        
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def process_video_clip(
    self,
    video_id: str,
    segment_id: int,
    input_s3_key: str,
    output_s3_key: str,
    enable_labeling: bool = True,
    enable_pii_blur: bool = True,
    blur_faces: bool = True,
    blur_plates: bool = True,
    enable_tracking: bool = True,
    confidence_threshold: float = 0.5,
    blur_intensity: int = 35
):
    """Process video clip with YOLOv8"""
    try:
        logger.info(f"Processing clip for video: {video_id}, segment: {segment_id}")
        
        # Download clip from S3
        temp_input = f"/tmp/{uuid.uuid4()}_input.mp4"
        temp_output = f"/tmp/{uuid.uuid4()}_output.mp4"
        
        aws_service.download_from_s3(input_s3_key, temp_input)
        
        # Process with YOLOv8
        success = video_service.process_video_with_yolov8(
            input_path=temp_input,
            output_path=temp_output,
            enable_labeling=enable_labeling,
            enable_pii_blur=enable_pii_blur,
            blur_faces=blur_faces,
            blur_plates=blur_plates,
            enable_tracking=enable_tracking,
            confidence_threshold=confidence_threshold,
            blur_kernel=blur_intensity
        )
        
        if success:
            # Upload processed clip to S3
            aws_service.upload_file_to_s3(temp_output, output_s3_key)
            logger.info(f"Processed clip uploaded: {output_s3_key}")
        
        # Clean up
        for f in [temp_input, temp_output]:
            if os.path.exists(f):
                os.remove(f)
        
        return {"status": "success" if success else "failed", "video_id": video_id}

    except Exception as e:
        logger.error(f"Error processing clip: {e}")
        raise


@celery_app.task(bind=True, base=DatabaseTask)
def process_search_clip_async(self, clip_s3_key: str, video_id: str, segment_id: int):
    """
    Asynchronously process a search result clip with YOLOv8 object detection and PII blurring
    """
    logger.info(f"🚀 Starting async processing for clip: {clip_s3_key}")

    try:
        # Generate processed clip S3 key
        processed_s3_key = clip_s3_key.replace('.mp4', '_processed.mp4')

        # Check if already processed
        try:
            aws_service.s3_client.head_object(Bucket=aws_service.bucket_name, Key=processed_s3_key)
            logger.info(f"✅ Processed clip already exists: {processed_s3_key}")
            return {
                "status": "success",
                "processed_s3_key": processed_s3_key,
                "message": "Already processed"
            }
        except:
            pass  # Doesn't exist, continue with processing

        # Create temporary files
        import tempfile
        temp_input = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_output = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)

        try:
            # Download original clip
            logger.info(f"📥 Downloading clip from S3: {clip_s3_key}")
            aws_service.download_from_s3(clip_s3_key, temp_input.name)

            # Process with YOLOv8
            logger.info(f"🔄 Processing clip with YOLOv8...")
            success = video_service.process_video_with_yolov8(
                input_path=temp_input.name,
                output_path=temp_output.name,
                enable_labeling=True,
                enable_tracking=True,
                enable_pii_blur=True,
                blur_faces=True,
                blur_plates=True,
                confidence_threshold=0.5,
                blur_kernel=35
            )

            if success:
                # Upload processed clip
                logger.info(f"📤 Uploading processed clip to S3: {processed_s3_key}")
                aws_service.upload_file_to_s3(temp_output.name, processed_s3_key)

                logger.info(f"✅ Successfully processed and uploaded: {processed_s3_key}")
                return {
                    "status": "success",
                    "processed_s3_key": processed_s3_key,
                    "video_id": video_id,
                    "segment_id": segment_id
                }
            else:
                logger.error(f"❌ YOLOv8 processing failed for: {clip_s3_key}")
                return {
                    "status": "failed",
                    "error": "YOLOv8 processing failed",
                    "video_id": video_id,
                    "segment_id": segment_id
                }

        finally:
            # Clean up temporary files
            for temp_file in [temp_input, temp_output]:
                try:
                    if os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)
                except:
                    pass

    except Exception as e:
        logger.error(f"❌ Error in async clip processing: {e}")
        return {
            "status": "failed",
            "error": str(e),
            "video_id": video_id,
            "segment_id": segment_id
        }
