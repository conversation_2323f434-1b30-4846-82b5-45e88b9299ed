import redis
import json
from typing import Optional, Any
from app.core.config import settings
from loguru import logger


class RedisService:
    """Service for Redis caching - metadata only, NOT video files"""
    
    def __init__(self):
        try:
            self.client = redis.from_url(
                settings.REDIS_URL,
                decode_responses=True,
                socket_connect_timeout=5
            )
            # Test connection
            self.client.ping()
            logger.info("✅ Redis service initialized")
        except Exception as e:
            logger.warning(f"⚠️ Redis not available: {e}. Caching disabled.")
            self.client = None
    
    def get_search_results(self, query: str, model: str, top_k: int) -> Optional[dict]:
        """
        Get cached search results metadata (NOT video files)
        Returns: dict with video_ids, segment_ids, timestamps, etc.
        """
        if not self.client:
            return None
        
        try:
            cache_key = f"search:{query}:{model}:{top_k}"
            cached = self.client.get(cache_key)
            
            if cached:
                logger.info(f"🎯 Redis cache HIT for query: '{query}'")
                return json.loads(cached)
            
            logger.info(f"❌ Redis cache MISS for query: '{query}'")
            return None
            
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
    
    def cache_search_results(self, query: str, model: str, top_k: int, results: dict, ttl: int = 3600):
        """
        Cache search results metadata for 1 hour (default)
        Only stores: video_ids, segment_ids, timestamps, similarity scores
        Does NOT store: video clips (those stay in S3)
        """
        if not self.client:
            return
        
        try:
            cache_key = f"search:{query}:{model}:{top_k}"
            self.client.setex(
                cache_key,
                ttl,
                json.dumps(results)
            )
            logger.info(f"💾 Cached search metadata in Redis (TTL: {ttl}s)")
            
        except Exception as e:
            logger.error(f"Redis set error: {e}")
    
    def invalidate_search_cache(self, pattern: str = "search:*"):
        """Invalidate search cache (e.g., when new videos indexed)"""
        if not self.client:
            return
        
        try:
            keys = self.client.keys(pattern)
            if keys:
                self.client.delete(*keys)
                logger.info(f"🗑️ Invalidated {len(keys)} cache entries")
        except Exception as e:
            logger.error(f"Redis invalidate error: {e}")
    
    def get_clip_metadata(self, video_id: str, segment_id: int) -> Optional[dict]:
        """Get cached clip metadata (S3 keys, URLs, etc.)"""
        if not self.client:
            return None
        
        try:
            cache_key = f"clip:{video_id}:{segment_id}"
            cached = self.client.get(cache_key)
            
            if cached:
                return json.loads(cached)
            return None
            
        except Exception as e:
            logger.error(f"Redis get clip metadata error: {e}")
            return None
    
    def cache_clip_metadata(self, video_id: str, segment_id: int, metadata: dict, ttl: int = 86400):
        """
        Cache clip metadata (S3 keys, presigned URLs, etc.) for 24 hours
        NOT the actual video file - just the metadata
        """
        if not self.client:
            return
        
        try:
            cache_key = f"clip:{video_id}:{segment_id}"
            self.client.setex(
                cache_key,
                ttl,
                json.dumps(metadata)
            )
        except Exception as e:
            logger.error(f"Redis set clip metadata error: {e}")


# Singleton instance
redis_service = RedisService()
