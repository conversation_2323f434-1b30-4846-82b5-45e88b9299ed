import weaviate
from typing import List, Dict, Any, Optional
import numpy as np
from app.core.config import settings
from loguru import logger


class VectorService:
    """Service for Weaviate vector database operations"""
    
    def __init__(self):
        self.client = weaviate.Client(url=settings.WEAVIATE_URL)
        self.collection_name = settings.WEAVIATE_COLLECTION
        self._ensure_schema()
        logger.info("Weaviate Service initialized")
    
    def _ensure_schema(self):
        """Ensure schema exists"""
        try:
            # Check if class exists
            schema = self.client.schema.get()
            class_names = [c['class'] for c in schema.get('classes', [])]
            
            if self.collection_name in class_names:
                logger.info(f"Class {self.collection_name} already exists")
                return
            
            # Create class
            class_obj = {
                "class": self.collection_name,
                "vectorizer": "none",
                "properties": [
                    {"name": "video_id", "dataType": ["text"]},
                    {"name": "segment_id", "dataType": ["int"]},
                    {"name": "timestamp", "dataType": ["text"]},
                    {"name": "start_sec", "dataType": ["number"]},
                    {"name": "model_type", "dataType": ["text"]},
                    {"name": "s3_key", "dataType": ["text"]},
                    {"name": "upload_time", "dataType": ["text"]},
                ]
            }
            self.client.schema.create_class(class_obj)
            logger.info(f"Created class {self.collection_name}")
            
        except Exception as e:
            # If class already exists, that's fine
            if "already exists" in str(e).lower():
                logger.info(f"Class {self.collection_name} already exists (caught in exception)")
                return
            logger.error(f"Error ensuring schema: {e}")
            raise
    
    def add_embeddings(self, embeddings_data: List[Dict[str, Any]]):
        """Add embeddings to Weaviate"""
        try:
            self.client.batch.configure(batch_size=100)
            with self.client.batch as batch:
                for item in embeddings_data:
                    # Normalize embedding vector
                    vec = np.array(item['embedding'])
                    norm = np.linalg.norm(vec)
                    if norm == 0:
                        logger.warning(f"Skipping zero-norm vector for segment {item.get('segment_id')}")
                        continue
                    
                    normalized_vector = (vec / norm).tolist()
                    
                    properties = {
                        "video_id": item['video_id'],
                        "segment_id": item['segment_id'],
                        "timestamp": item['timestamp'],
                        "start_sec": item['start_sec'],
                        "model_type": item['model_type'],
                        "s3_key": item['s3_key'],
                        "upload_time": item['upload_time'],
                    }
                    
                    batch.add_data_object(
                        data_object=properties,
                        class_name=self.collection_name,
                        vector=normalized_vector
                    )
            
            logger.info(f"Added {len(embeddings_data)} embeddings to Weaviate")
            
        except Exception as e:
            logger.error(f"Error adding embeddings: {e}")
            raise
    
    def search(self, query_embedding: List[float], top_k: int = 5, model_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search for similar vectors"""
        try:
            logger.info(f"🔎 Vector search starting...")
            logger.info(f"   Requested top_k: {top_k}")
            logger.info(f"   Model type filter: {model_type} (DISABLED)")
            
            # Normalize query embedding
            vec = np.array(query_embedding)
            norm = np.linalg.norm(vec)
            if norm == 0:
                logger.error("❌ Query embedding has zero norm")
                return []
            
            logger.info(f"   Query vector norm: {norm:.4f}")
            normalized_query = (vec / norm).tolist()
            
            # Build query
            near_vector = {"vector": normalized_query}
            
            query_builder = (
                self.client.query
                .get(self.collection_name, ["video_id", "segment_id", "timestamp", "start_sec", "model_type", "s3_key"])
                .with_near_vector(near_vector)
                .with_limit(top_k * 3)
                .with_additional(["distance"])
            )
            
            # Temporarily disable model type filter to match Streamlit behavior
            # Streamlit searches across all embeddings without filtering by model
            # if model_type:
            #     query_builder = query_builder.with_where({
            #         "path": ["model_type"],
            #         "operator": "Equal",
            #         "valueText": model_type
            #     })
            
            logger.info(f"   Executing Weaviate query (no model filter - like Streamlit)...")
            response = query_builder.do()
            
            logger.info(f"   Weaviate response received")
            
            # Process results
            results = []
            if "data" in response and "Get" in response["data"]:
                objects = response["data"]["Get"].get(self.collection_name, [])
                logger.info(f"   Found {len(objects)} objects in Weaviate response")
                
                for obj in objects:
                    distance = obj.get("_additional", {}).get("distance", 0.0)
                    similarity = 1 - float(distance)
                    
                    results.append({
                        'video_id': obj['video_id'],
                        'segment_id': obj['segment_id'],
                        'timestamp': obj['timestamp'],
                        'start_sec': obj['start_sec'],
                        'model_type': obj['model_type'],
                        's3_key': obj['s3_key'],
                        'similarity': similarity,
                        'distance': distance
                    })
            
            # Deduplicate results
            results = self._deduplicate_results(results, time_threshold=1.5)
            
            # Limit to top_k
            results = results[:top_k]
            
            logger.info(f"Found {len(results)} results for query")
            return results
            
        except Exception as e:
            logger.error(f"Error searching vectors: {e}")
            return []
    
    def _deduplicate_results(self, results: List[Dict[str, Any]], time_threshold: float = 1.5) -> List[Dict[str, Any]]:
        """Remove duplicate clips with similar timestamps"""
        if not results:
            return results
        
        # Sort by similarity (higher first)
        results.sort(key=lambda x: x['similarity'], reverse=True)
        
        # Group by video_id
        video_groups = {}
        for result in results:
            video_id = result['video_id']
            if video_id not in video_groups:
                video_groups[video_id] = []
            video_groups[video_id].append(result)
        
        # Select best clips from each video
        unique_results = []
        for video_id, video_clips in video_groups.items():
            # Sort clips by timestamp
            video_clips.sort(key=lambda x: x['start_sec'])
            
            selected_clips = []
            for clip in video_clips:
                # Check if too close to any selected clip
                too_close = False
                for selected in selected_clips:
                    if abs(selected['start_sec'] - clip['start_sec']) < time_threshold:
                        # Keep the one with higher similarity
                        if clip['similarity'] > selected['similarity']:
                            selected_clips.remove(selected)
                            selected_clips.append(clip)
                        too_close = True
                        break
                
                if not too_close:
                    selected_clips.append(clip)
            
            unique_results.extend(selected_clips)
        
        # Sort final results by similarity
        unique_results.sort(key=lambda x: x['similarity'], reverse=True)
        
        return unique_results
    
    def delete_by_video_id(self, video_id: str):
        """Delete all embeddings for a video"""
        try:
            self.client.batch.delete_objects(
                class_name=self.collection_name,
                where={
                    "path": ["video_id"],
                    "operator": "Equal",
                    "valueText": video_id
                }
            )
            logger.info(f"Deleted embeddings for video {video_id}")
        except Exception as e:
            logger.error(f"Error deleting embeddings: {e}")
            # Don't raise, just log - deletion is best effort
    
    def get_video_embeddings_count(self, video_id: str) -> int:
        """Get count of embeddings for a video"""
        try:
            response = (
                self.client.query
                .aggregate(self.collection_name)
                .with_where({
                    "path": ["video_id"],
                    "operator": "Equal",
                    "valueText": video_id
                })
                .with_meta_count()
                .do()
            )
            
            count = response.get('data', {}).get('Aggregate', {}).get(self.collection_name, [{}])[0].get('meta', {}).get('count', 0)
            return count
            
        except Exception as e:
            logger.error(f"Error getting embeddings count: {e}")
            return 0
    
    def get_video_embeddings(self, video_id: str, model: str = "marengo") -> List[Dict[str, Any]]:
        """
        Fetch all embeddings for a video from Weaviate.
        
        Args:
            video_id: Video ID to fetch embeddings for
            model: Model type to filter by ("nova-premier" or "marengo")
        
        Returns:
            List of embeddings with timestamps and vectors
        """
        try:
            logger.info(f"Fetching {model} embeddings for video: {video_id}")
            
            # Query Weaviate for all embeddings for this video and model
            response = (
                self.client.query
                .get(self.collection_name, ["video_id", "segment_id", "timestamp", "start_sec", "model_type"])
                .with_additional(["vector"])
                .with_where({
                    "operator": "And",
                    "operands": [
                        {
                            "path": ["video_id"],
                            "operator": "Equal",
                            "valueText": video_id
                        },
                        {
                            "path": ["model_type"],
                            "operator": "Equal",
                            "valueText": model
                        }
                    ]
                })
                .with_limit(10000)  # Get all embeddings
                .do()
            )
            
            # Extract embeddings
            embeddings = []
            results = response.get('data', {}).get('Get', {}).get(self.collection_name, [])
            
            for result in results:
                embeddings.append({
                    'video_id': result.get('video_id'),
                    'segment_id': result.get('segment_id'),
                    'timestamp': result.get('start_sec', 0.0),
                    'frame_number': result.get('segment_id', 0),
                    'embedding': result.get('_additional', {}).get('vector', [])
                })
            
            logger.info(f"Found {len(embeddings)} {model} embeddings for {video_id}")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error fetching video embeddings: {e}")
            return []


# Singleton instance
vector_service = VectorService()
