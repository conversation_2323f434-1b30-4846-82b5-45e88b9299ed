"""PII Redaction Service for GDPR/CCPA Compliance"""
import cv2
import numpy as np
import os
from typing import Optional, <PERSON><PERSON>
from loguru import logger


class PIIRedactionService:
    """
    Service for redacting Personally Identifiable Information (PII) from videos
    
    Compliance Standards:
    - GDPR (General Data Protection Regulation - EU)
    - CCPA (California Consumer Privacy Act - US)
    - General privacy best practices
    
    Features:
    - Face detection and blurring
    - License plate detection and redaction
    - Configurable blur intensity
    """
    
    def __init__(self):
        """Initialize PII redaction models"""
        try:
            # Load Haar Cascade for face detection
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)
            
            if self.face_cascade.empty():
                raise ValueError("Failed to load face cascade classifier")
            
            logger.info("✅ PII Redaction Service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize PII service: {e}")
            raise
    
    def blur_faces(
        self, 
        frame: np.ndarray,
        blur_intensity: int = 99
    ) -> Tuple[np.ndarray, int]:
        """
        Detect and blur all faces in a frame
        
        Args:
            frame: Input video frame
            blur_intensity: Gaussian blur kernel size (must be odd, higher = more blur)
        
        Returns:
            (blurred_frame, num_faces_detected)
        """
        # Ensure blur intensity is odd
        if blur_intensity % 2 == 0:
            blur_intensity += 1
        
        # Convert to grayscale for detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Detect faces
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.3,
            minNeighbors=5,
            minSize=(30, 30)
        )
        
        # Blur each detected face
        for (x, y, w, h) in faces:
            # Add padding around face
            padding = int(w * 0.2)
            x1 = max(0, x - padding)
            y1 = max(0, y - padding)
            x2 = min(frame.shape[1], x + w + padding)
            y2 = min(frame.shape[0], y + h + padding)
            
            # Extract face region
            face_region = frame[y1:y2, x1:x2]
            
            # Apply Gaussian blur
            blurred_face = cv2.GaussianBlur(
                face_region,
                (blur_intensity, blur_intensity),
                30
            )
            
            # Replace face in frame
            frame[y1:y2, x1:x2] = blurred_face
        
        return frame, len(faces)
    
    def blur_license_plates(
        self,
        frame: np.ndarray,
        blur_intensity: int = 51
    ) -> Tuple[np.ndarray, int]:
        """
        Detect and blur license plates in a frame
        
        Note: This is a simplified implementation. For production,
        consider using specialized license plate detection models.
        
        Args:
            frame: Input video frame
            blur_intensity: Gaussian blur kernel size
        
        Returns:
            (blurred_frame, num_plates_detected)
        """
        # Ensure blur intensity is odd
        if blur_intensity % 2 == 0:
            blur_intensity += 1
        
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Apply edge detection
        edges = cv2.Canny(gray, 50, 150)
        
        # Find contours
        contours, _ = cv2.findContours(
            edges,
            cv2.RETR_TREE,
            cv2.CHAIN_APPROX_SIMPLE
        )
        
        plates_detected = 0
        
        # Look for rectangular contours (potential plates)
        for contour in contours:
            # Approximate contour to polygon
            peri = cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, 0.02 * peri, True)
            
            # Check if it's a rectangle
            if len(approx) == 4:
                x, y, w, h = cv2.boundingRect(contour)
                
                # Check aspect ratio (typical license plate ratio)
                aspect_ratio = w / float(h)
                
                # License plates are typically 2:1 to 5:1 ratio
                if 2.0 <= aspect_ratio <= 5.0 and w > 50 and h > 15:
                    # Blur the region
                    plate_region = frame[y:y+h, x:x+w]
                    blurred_plate = cv2.GaussianBlur(
                        plate_region,
                        (blur_intensity, blur_intensity),
                        30
                    )
                    frame[y:y+h, x:x+w] = blurred_plate
                    plates_detected += 1
        
        return frame, plates_detected
    
    def redact_pii(
        self,
        frame: np.ndarray,
        blur_faces: bool = True,
        blur_plates: bool = True
    ) -> Tuple[np.ndarray, Dict[str, int]]:
        """
        Apply all PII redaction to a frame
        
        Args:
            frame: Input video frame
            blur_faces: Whether to blur faces
            blur_plates: Whether to blur license plates
        
        Returns:
            (redacted_frame, stats)
            stats = {'faces': 2, 'plates': 1}
        """
        stats = {'faces': 0, 'plates': 0}
        
        if blur_faces:
            frame, num_faces = self.blur_faces(frame)
            stats['faces'] = num_faces
        
        if blur_plates:
            frame, num_plates = self.blur_license_plates(frame)
            stats['plates'] = num_plates
        
        return frame, stats
    
    def process_video_segment(
        self,
        input_path: str,
        output_path: str,
        start_sec: float = 0.0,
        duration: float = 10.0,
        blur_faces: bool = True,
        blur_plates: bool = True
    ) -> Dict[str, any]:
        """
        Process a video segment with PII redaction
        
        Args:
            input_path: Path to input video
            output_path: Path to save redacted video
            start_sec: Start time in seconds
            duration: Duration to process in seconds
            blur_faces: Whether to blur faces
            blur_plates: Whether to blur license plates
        
        Returns:
            {
                'success': True,
                'output_path': '/path/to/output.mp4',
                'frames_processed': 300,
                'total_faces_blurred': 45,
                'total_plates_blurred': 3,
                'duration': 10.0
            }
        """
        cap = cv2.VideoCapture(input_path)
        
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {input_path}")
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Seek to start position
        cap.set(cv2.CAP_PROP_POS_MSEC, start_sec * 1000)
        
        # Setup video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        # Process frames
        frame_count = 0
        max_frames = int(duration * fps)
        total_faces = 0
        total_plates = 0
        
        logger.info(f"Processing video segment: {start_sec}s to {start_sec + duration}s")
        
        while frame_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Apply PII redaction
            frame, stats = self.redact_pii(frame, blur_faces, blur_plates)
            total_faces += stats['faces']
            total_plates += stats['plates']
            
            out.write(frame)
            frame_count += 1
            
            if frame_count % 30 == 0:
                logger.debug(f"Processed {frame_count}/{max_frames} frames")
        
        cap.release()
        out.release()
        
        logger.info(f"✅ PII redaction complete: {total_faces} faces, {total_plates} plates blurred")
        
        return {
            'success': True,
            'output_path': output_path,
            'frames_processed': frame_count,
            'total_faces_blurred': total_faces,
            'total_plates_blurred': total_plates,
            'duration': frame_count / fps
        }


# Singleton instance
pii_service = PIIRedactionService()
