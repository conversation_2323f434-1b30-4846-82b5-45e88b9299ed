"""
Streaming Upload Service
Handles chunked file uploads directly to S3 without storing in temp files
"""
import aioboto3
from typing import As<PERSON><PERSON><PERSON><PERSON>
from fastapi import UploadFile
from loguru import logger
from app.core.config import settings


class UploadService:
    """Service for handling streaming uploads to S3"""
    
    def __init__(self):
        self.session = aioboto3.Session()
        self.chunk_size = 10 * 1024 * 1024  # 10MB chunks
    
    async def stream_to_s3(
        self,
        file: UploadFile,
        s3_key: str,
        progress_callback=None
    ) -> dict:
        """
        Stream file directly to S3 using multipart upload
        
        Args:
            file: FastAPI UploadFile object
            s3_key: S3 object key
            progress_callback: Optional callback for progress updates
            
        Returns:
            dict with upload metadata
        """
        try:
            logger.info(f"Starting streaming upload to S3: {s3_key}")
            
            async with self.session.client('s3', region_name=settings.BEDROCK_REGION) as s3:
                # Initiate multipart upload
                mpu = await s3.create_multipart_upload(
                    Bucket=settings.S3_BUCKET,
                    Key=s3_key
                )
                upload_id = mpu['UploadId']
                logger.info(f"Multipart upload initiated: {upload_id}")
                
                parts = []
                part_number = 1
                total_bytes = 0
                
                try:
                    # Stream file in chunks
                    while True:
                        chunk = await file.read(self.chunk_size)
                        if not chunk:
                            break
                        
                        # Upload part
                        response = await s3.upload_part(
                            Bucket=settings.S3_BUCKET,
                            Key=s3_key,
                            PartNumber=part_number,
                            UploadId=upload_id,
                            Body=chunk
                        )
                        
                        parts.append({
                            'PartNumber': part_number,
                            'ETag': response['ETag']
                        })
                        
                        total_bytes += len(chunk)
                        
                        # Progress callback
                        if progress_callback:
                            await progress_callback(part_number, total_bytes)
                        
                        logger.debug(f"Uploaded part {part_number}, {len(chunk)} bytes")
                        part_number += 1
                    
                    # Complete multipart upload
                    await s3.complete_multipart_upload(
                        Bucket=settings.S3_BUCKET,
                        Key=s3_key,
                        UploadId=upload_id,
                        MultipartUpload={'Parts': parts}
                    )
                    
                    logger.info(f"✅ Streaming upload complete: {s3_key}, {total_bytes} bytes")
                    
                    return {
                        'success': True,
                        's3_key': s3_key,
                        'size': total_bytes,
                        'parts': len(parts)
                    }
                    
                except Exception as e:
                    # Abort multipart upload on error
                    logger.error(f"Error during upload, aborting: {e}")
                    await s3.abort_multipart_upload(
                        Bucket=settings.S3_BUCKET,
                        Key=s3_key,
                        UploadId=upload_id
                    )
                    raise
                    
        except Exception as e:
            logger.error(f"Streaming upload failed: {e}")
            raise
    
    async def upload_with_temp_fallback(
        self,
        file: UploadFile,
        s3_key: str,
        temp_path: str = None
    ) -> dict:
        """
        Upload file to S3 with fallback to temp file if streaming fails
        
        Args:
            file: FastAPI UploadFile object
            s3_key: S3 object key
            temp_path: Optional temp file path for fallback
            
        Returns:
            dict with upload metadata
        """
        try:
            # Try streaming upload first
            return await self.stream_to_s3(file, s3_key)
        except Exception as e:
            logger.warning(f"Streaming upload failed, falling back to temp file: {e}")
            
            if not temp_path:
                raise
            
            # Fallback to traditional upload
            import os
            from app.services.aws_service import aws_service
            
            # Save to temp file
            with open(temp_path, 'wb') as f:
                content = await file.read()
                f.write(content)
            
            # Upload temp file
            aws_service.upload_file_to_s3(temp_path, s3_key)
            
            file_size = os.path.getsize(temp_path)
            
            return {
                'success': True,
                's3_key': s3_key,
                'size': file_size,
                'method': 'fallback'
            }


# Global instance
upload_service = UploadService()
