"""YOLOv8 Object Detection Service"""
import cv2
import numpy as np
from typing import List, Dict, Any, Optional
from loguru import logger
from ultralytics import YOLO


class YOLOService:
    """Service for YOLOv8 object detection in videos"""
    
    def __init__(self, model_path: str = "yolov8n.pt"):
        """Initialize YOLO model"""
        try:
            self.model = YOLO(model_path)
            logger.info(f"✅ YOLO model loaded: {model_path}")
        except Exception as e:
            logger.error(f"❌ Failed to load YOLO model: {e}")
            raise
    
    def detect_objects_in_frame(
        self, 
        frame: np.ndarray,
        confidence_threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        Detect objects in a single frame
        
        Returns:
            List of detections with format:
            {
                'class': 'person',
                'confidence': 0.95,
                'bbox': [x1, y1, x2, y2]
            }
        """
        results = self.model(frame, conf=confidence_threshold)
        detections = []
        
        for result in results:
            for box in result.boxes:
                detection = {
                    'class': result.names[int(box.cls)],
                    'confidence': float(box.conf),
                    'bbox': box.xyxy[0].tolist()  # [x1, y1, x2, y2]
                }
                detections.append(detection)
        
        return detections
    
    def detect_objects_in_video(
        self,
        video_path: str,
        sample_rate: int = 30,  # Process every Nth frame
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """
        Detect objects throughout a video
        
        Args:
            video_path: Path to video file
            sample_rate: Process every Nth frame (30 = ~1 frame per second at 30fps)
            confidence_threshold: Minimum confidence for detections
        
        Returns:
            {
                'total_frames': 1000,
                'processed_frames': 33,
                'detections_by_frame': {
                    0: [{'class': 'person', 'confidence': 0.95, ...}],
                    30: [{'class': 'car', 'confidence': 0.89, ...}],
                    ...
                },
                'object_summary': {
                    'person': 45,
                    'car': 23,
                    'truck': 5,
                    ...
                }
            }
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {video_path}")
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        
        detections_by_frame = {}
        object_counts = {}
        frame_idx = 0
        processed_frames = 0
        
        logger.info(f"Processing video: {total_frames} frames at {fps} fps")
        
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            
            # Sample frames
            if frame_idx % sample_rate == 0:
                detections = self.detect_objects_in_frame(frame, confidence_threshold)
                
                if detections:
                    detections_by_frame[frame_idx] = detections
                    
                    # Count objects
                    for det in detections:
                        obj_class = det['class']
                        object_counts[obj_class] = object_counts.get(obj_class, 0) + 1
                
                processed_frames += 1
                
                if processed_frames % 10 == 0:
                    logger.debug(f"Processed {processed_frames} frames...")
            
            frame_idx += 1
        
        cap.release()
        
        logger.info(f"✅ Detected objects: {object_counts}")
        
        return {
            'total_frames': total_frames,
            'processed_frames': processed_frames,
            'fps': fps,
            'detections_by_frame': detections_by_frame,
            'object_summary': object_counts
        }
    
    def detect_specific_objects(
        self,
        video_path: str,
        target_classes: List[str],
        sample_rate: int = 30,
        confidence_threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        Detect specific object classes in video
        
        Args:
            video_path: Path to video file
            target_classes: List of object classes to detect (e.g., ['person', 'car'])
            sample_rate: Process every Nth frame
            confidence_threshold: Minimum confidence
        
        Returns:
            List of timestamps where target objects were detected:
            [
                {'timestamp': 5.5, 'frame': 165, 'objects': ['person', 'car']},
                {'timestamp': 12.3, 'frame': 369, 'objects': ['person']},
                ...
            ]
        """
        cap = cv2.VideoCapture(video_path)
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        
        detections = []
        frame_idx = 0
        
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            
            if frame_idx % sample_rate == 0:
                frame_detections = self.detect_objects_in_frame(frame, confidence_threshold)
                
                # Filter for target classes
                found_objects = [
                    det['class'] for det in frame_detections 
                    if det['class'] in target_classes
                ]
                
                if found_objects:
                    detections.append({
                        'timestamp': frame_idx / fps,
                        'frame': frame_idx,
                        'objects': list(set(found_objects))  # Unique objects
                    })
            
            frame_idx += 1
        
        cap.release()
        
        return detections
    
    def get_available_classes(self) -> List[str]:
        """Get list of all object classes the model can detect"""
        return list(self.model.names.values())


# Singleton instance
yolo_service = YOLOService()
