import boto3
from botocore.config import Config
from typing import Optional, List, Dict, Any
import json
import os
from datetime import datetime
from loguru import logger
from app.core.config import settings


class AWSService:
    """Service for AWS operations"""
    
    def __init__(self):
        self.s3_bucket = settings.S3_BUCKET
        
        self.s3_client = boto3.client(
            's3',
            region_name=settings.BEDROCK_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        
        self.bedrock_client = boto3.client(
            'bedrock-runtime',
            region_name=settings.BEDROCK_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            config=Config(
                read_timeout=300,
                connect_timeout=60,
                retries={'max_attempts': 2}
            )
        )
        
        logger.info("AWS Service initialized")
    
    def generate_presigned_url(self, s3_key: str, expiration: int = 3600) -> str:
        """Generate presigned URL for S3 object"""
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': settings.S3_BUCKET,
                    'Key': s3_key,
                    'ResponseContentType': 'video/mp4',
                    'ResponseCacheControl': 'no-cache'
                },
                ExpiresIn=expiration
            )
            return url
        except Exception as e:
            logger.error(f"Error generating presigned URL: {e}")
            raise
    
    def upload_file_to_s3(self, file_path: str, s3_key: str, content_type: str = 'video/mp4'):
        """Upload file to S3"""
        try:
            self.s3_client.upload_file(
                file_path,
                settings.S3_BUCKET,
                s3_key,
                ExtraArgs={'ContentType': content_type}
            )
            logger.info(f"Uploaded file to S3 with Content-Type video/mp4: {s3_key}")
            return f"s3://{settings.S3_BUCKET}/{s3_key}"
        except Exception as e:
            logger.error(f"Error uploading file to S3: {e}")
            raise
    
    def s3_object_exists(self, s3_key: str) -> bool:
        """Check if S3 object exists"""
        try:
            self.s3_client.head_object(Bucket=settings.S3_BUCKET, Key=s3_key)
            return True
        except:
            return False
    
    def upload_json_to_s3(self, data: dict, s3_key: str):
        """Upload JSON data to S3"""
        try:
            self.s3_client.put_object(
                Bucket=settings.S3_BUCKET,
                Key=s3_key,
                Body=json.dumps(data),
                ContentType='application/json'
            )
            logger.info(f"Uploaded JSON to S3: {s3_key}")
        except Exception as e:
            logger.error(f"Error uploading JSON to S3: {e}")
            raise
    
    def download_json_from_s3(self, s3_key: str) -> Dict[Any, Any]:
        """Download JSON data from S3"""
        try:
            response = self.s3_client.get_object(Bucket=settings.S3_BUCKET, Key=s3_key)
            return json.loads(response['Body'].read().decode('utf-8'))
        except Exception as e:
            logger.error(f"Error downloading JSON from S3: {e}")
            raise
    
    def download_from_s3(self, s3_key: str, local_path: str):
        """Download file from S3 to local path"""
        try:
            self.s3_client.download_file(
                settings.S3_BUCKET,
                s3_key,
                local_path
            )
            logger.info(f"Downloaded from S3: {s3_key} -> {local_path}")
        except Exception as e:
            logger.error(f"Error downloading from S3: {e}")
            raise
    
    def generate_text_embedding(self, text: str, model: str = "marengo") -> Optional[List[float]]:
        """Generate embedding for text query using Marengo"""
        try:
            # Use Marengo cross-region inference profile (same as original app)
            model_id = 'us.twelvelabs.marengo-embed-2-7-v1:0'
            
            # Correct format from original app
            body = json.dumps({
                'inputType': 'text',
                'inputText': text  # Must be inputText, not just text!
            })
            
            response = self.bedrock_client.invoke_model(
                modelId=model_id,
                body=body,
                contentType='application/json',
                accept='application/json'
            )
            
            result = json.loads(response['body'].read())
            embedding = result['data'][0]['embedding']
            logger.info(f"Generated text embedding using Marengo")
            return embedding
            
        except Exception as e:
            logger.error(f"Error generating text embedding: {e}")
            return None
    
    def start_async_embedding_job(self, video_id: str, s3_key: str, model: str = "marengo") -> Optional[str]:
        """Start embedding generation job with AWS Bedrock using async invoke"""
        try:
            s3_uri = f"s3://{settings.S3_BUCKET}/{s3_key}"
            
            # Select correct model based on parameter
            if model == "nova":
                model_id = 'amazon.nova-premier-v1:0'
                logger.info(f"Using Nova Premier model for {video_id}")
            else:  # marengo
                model_id = 'twelvelabs.marengo-embed-2-7-v1:0'
                logger.info(f"Using Marengo model for {video_id}")
            
            # Model input for Marengo
            model_input = {
                "inputType": "video",
                "mediaSource": {
                    "s3Location": {
                        "uri": s3_uri,
                        "bucketOwner": settings.AWS_ACCOUNT_ID
                    }
                }
            }
            
            # Output configuration for async job
            output_config = {
                "s3OutputDataConfig": {
                    "s3Uri": f"s3://{settings.S3_BUCKET}/embeddings-output/{video_id}/"
                }
            }
            
            logger.info(f"Starting {model.upper()} async embedding job for {video_id}")
            logger.info(f"Model ID: {model_id}")
            logger.info(f"Input: {s3_uri}")
            logger.info(f"Output: s3://{settings.S3_BUCKET}/embeddings-output/{video_id}/")
            
            # Call AWS Bedrock async invoke API
            try:
                response = self.bedrock_client.start_async_invoke(
                    modelId=model_id,
                    modelInput=model_input,
                    outputDataConfig=output_config
                )
                
                invocation_arn = response['invocationArn']
                logger.info(f"✅ {model.upper()} async job started: {invocation_arn}")
                
                # Save job info to S3
                job_data = {
                    'video_id': video_id,
                    'invocation_arn': invocation_arn,
                    'model': model,
                    'embedding_model': model,  # Store actual model used
                    'model_id': model_id,
                    'timestamp': datetime.now().isoformat(),
                    'expected_output': f"s3://{settings.S3_BUCKET}/embeddings-output/{video_id}/output.json"
                }
                job_key = f'embeddings-output/{video_id}/job_info.json'
                self.upload_json_to_s3(job_data, job_key)
                
                return invocation_arn
                
            except AttributeError as e:
                # start_async_invoke might not be available in older boto3 versions
                logger.error(f"start_async_invoke not available: {e}")
                logger.error("Please upgrade boto3: pip install --upgrade boto3")
                logger.error("Or check if you have access to AWS Bedrock async APIs")
                return None
            
        except Exception as e:
            logger.error(f"Error starting async embedding job: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def detect_video_codec(self, s3_key: str) -> Optional[str]:
        """
        Detect video codec using FFmpeg probe.
        Returns codec name (e.g., 'h264', 'av01', 'hevc') or None if detection fails.
        """
        import subprocess
        import tempfile
        import os

        try:
            # Download a small portion of the video for codec detection
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
                temp_path = temp_file.name

            # Download first 1MB for codec detection
            try:
                response = self.s3_client.get_object(
                    Bucket=settings.S3_BUCKET,
                    Key=s3_key,
                    Range='bytes=0-1048576'  # First 1MB
                )
                with open(temp_path, 'wb') as f:
                    f.write(response['Body'].read())

                # Use FFprobe to detect codec
                cmd = [
                    'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                    '-show_entries', 'stream=codec_name', '-of', 'csv=p=0',
                    temp_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    codec = result.stdout.strip()
                    logger.info(f"Detected video codec for {s3_key}: {codec}")
                    return codec
                else:
                    logger.warning(f"FFprobe failed for {s3_key}: {result.stderr}")
                    return None

            finally:
                # Clean up temp file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except Exception as e:
            logger.error(f"Error detecting codec for {s3_key}: {e}")
            return None

    def generate_video_summary(self, video_id: str, prompt: str = None, s3_key: str = None) -> Optional[str]:
        """
        IMPROVED: Generate video summary using AWS Bedrock Converse API with retry logic and better error handling.
        Supports S3 URIs for large videos with timeout and retry mechanisms.
        Now includes AV1 codec detection and proper error handling.
        """
        import time
        from botocore.exceptions import ClientError, BotoCoreError

        if not s3_key:
            logger.error("s3_key must be provided for video analysis")
            return None

        # Validate S3 object exists before attempting analysis
        if not self.s3_object_exists(s3_key):
            logger.error(f"S3 object does not exist: {s3_key}")
            return None

        # Detect video codec to check for AV1 compatibility issues
        codec = self.detect_video_codec(s3_key)
        if codec and codec.lower() in ['av01', 'av1']:
            logger.warning(f"AV1 codec detected for {video_id}. Nova Premier may have compatibility issues.")
            # Continue with analysis but expect potential failures

        # Use Nova Premier through Converse API (supports S3 URIs)
        model_id = 'us.amazon.nova-premier-v1:0'

        # Prepare message with S3 video source
        s3_uri = f"s3://{settings.S3_BUCKET}/{s3_key}"
        logger.info(f"Using Converse API for video analysis: {s3_uri}")

        messages = [{
            "role": "user",
            "content": [
                {
                    "video": {
                        "format": "mp4",
                        "source": {
                            "s3Location": {
                                "uri": s3_uri,
                                "bucketOwner": settings.AWS_ACCOUNT_ID
                            }
                        }
                    }
                },
                {
                    "text": prompt if prompt else "Analyze this dashcam video and provide a brief safety summary."
                }
            ]
        }]

        # System prompt for consistent output
        system = [{
            "text": "You are a professional dashcam video analyst. Provide factual, structured analysis in Markdown format. Focus on safety incidents, driving behaviors, and traffic conditions. Be specific but concise."
        }]

        # Inference config with timeout considerations
        inference_config = {
            "maxTokens": 2000,  # Increased for detailed analysis
            "temperature": 0.1,  # Low temperature for consistency
            "topP": 0.8
        }

        # Retry logic for transient failures
        max_retries = 3
        retry_delay = 2  # seconds

        for attempt in range(max_retries):
            try:
                logger.info(f"Attempt {attempt + 1}/{max_retries} for video analysis: {video_id}")

                # Call Converse API with timeout
                response = self.bedrock_client.converse(
                    modelId=model_id,
                    messages=messages,
                    system=system,
                    inferenceConfig=inference_config
                )

                # Extract and validate response
                if 'output' in response and 'message' in response['output']:
                    content = response['output']['message']['content']
                    if content and len(content) > 0 and 'text' in content[0]:
                        summary = content[0]['text'].strip()

                        # Validate summary quality
                        if len(summary) > 50:  # Minimum meaningful length
                            logger.info(f"✅ Generated video summary for {video_id} (attempt {attempt + 1})")
                            return summary
                        else:
                            logger.warning(f"Summary too short ({len(summary)} chars), retrying...")

                logger.warning(f"No valid summary content in response for {video_id}")

            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', 'Unknown')
                error_message = e.response.get('Error', {}).get('Message', str(e))

                if error_code in ['ThrottlingException', 'ServiceUnavailableException']:
                    logger.warning(f"AWS throttling/service error (attempt {attempt + 1}): {error_message}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                        continue
                elif error_code == 'ValidationException':
                    logger.error(f"Validation error - not retrying: {error_message}")
                    break
                else:
                    logger.error(f"AWS client error (attempt {attempt + 1}): {error_code} - {error_message}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue

            except BotoCoreError as e:
                logger.error(f"Boto core error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue

            except Exception as e:
                logger.error(f"Unexpected error in video summary generation (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue

            # If we get here, this attempt failed
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
            else:
                logger.error(f"All {max_retries} attempts failed for video {video_id}")

        return None


# Singleton instance
aws_service = AWSService()
