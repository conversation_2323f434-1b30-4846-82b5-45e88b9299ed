"""add_dual_embedding_fields

Revision ID: 777fbafffa25
Revises: 8805898f2564
Create Date: 2025-11-01 05:07:46.284213

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '777fbafffa25'
down_revision = '8805898f2564'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('videos', sa.Column('nova_embedding_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('nova_embedding_completed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('nova_embedding_arn', sa.String(), nullable=True))
    op.add_column('videos', sa.Column('nova_indexed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('marengo_embedding_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('marengo_embedding_completed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('marengo_embedding_arn', sa.String(), nullable=True))
    op.add_column('videos', sa.Column('marengo_indexed_at', sa.DateTime(timezone=True), nullable=True))
    op.drop_column('videos', 'indexed_at')
    op.drop_column('videos', 'embedding_started_at')
    op.drop_column('videos', 'embedding_completed_at')
    op.drop_column('videos', 'embedding_model')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('videos', sa.Column('embedding_model', postgresql.ENUM('NOVA_PREMIER', 'MARENGO', name='embeddingmodel'), autoincrement=False, nullable=True))
    op.add_column('videos', sa.Column('embedding_completed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('videos', sa.Column('embedding_started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('videos', sa.Column('indexed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.drop_column('videos', 'marengo_indexed_at')
    op.drop_column('videos', 'marengo_embedding_arn')
    op.drop_column('videos', 'marengo_embedding_completed_at')
    op.drop_column('videos', 'marengo_embedding_started_at')
    op.drop_column('videos', 'nova_indexed_at')
    op.drop_column('videos', 'nova_embedding_arn')
    op.drop_column('videos', 'nova_embedding_completed_at')
    op.drop_column('videos', 'nova_embedding_started_at')
    # ### end Alembic commands ###
