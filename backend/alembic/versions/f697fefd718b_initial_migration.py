"""Initial migration

Revision ID: f697fefd718b
Revises: 
Create Date: 2025-10-31 06:35:16.279804

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f697fefd718b'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('processing_jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_id', sa.String(), nullable=False),
    sa.Column('video_id', sa.String(), nullable=False),
    sa.Column('job_type', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('invocation_arn', sa.String(), nullable=True),
    sa.Column('progress', sa.Float(), nullable=True),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('result_s3_key', sa.String(), nullable=True),
    sa.Column('error', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_processing_jobs_id'), 'processing_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_processing_jobs_job_id'), 'processing_jobs', ['job_id'], unique=True)
    op.create_index(op.f('ix_processing_jobs_video_id'), 'processing_jobs', ['video_id'], unique=False)
    op.create_table('search_ratings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('query', sa.Text(), nullable=False),
    sa.Column('query_hash', sa.String(), nullable=False),
    sa.Column('video_id', sa.String(), nullable=False),
    sa.Column('segment_id', sa.Integer(), nullable=False),
    sa.Column('model_type', sa.Enum('NOVA_PREMIER', 'MARENGO', name='embeddingmodel'), nullable=False),
    sa.Column('rating', sa.Integer(), nullable=False),
    sa.Column('similarity_score', sa.Float(), nullable=False),
    sa.Column('user_session', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_search_ratings_id'), 'search_ratings', ['id'], unique=False)
    op.create_index(op.f('ix_search_ratings_query_hash'), 'search_ratings', ['query_hash'], unique=False)
    op.create_index(op.f('ix_search_ratings_video_id'), 'search_ratings', ['video_id'], unique=False)
    op.create_table('videos',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('video_id', sa.String(), nullable=False),
    sa.Column('filename', sa.String(), nullable=False),
    sa.Column('original_filename', sa.String(), nullable=False),
    sa.Column('s3_key', sa.String(), nullable=False),
    sa.Column('s3_uri', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('UPLOADING', 'UPLOADED', 'PROCESSING', 'INDEXED', 'FAILED', name='videostatus'), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('duration', sa.Float(), nullable=True),
    sa.Column('width', sa.Integer(), nullable=True),
    sa.Column('height', sa.Integer(), nullable=True),
    sa.Column('fps', sa.Float(), nullable=True),
    sa.Column('embedding_model', sa.Enum('NOVA_PREMIER', 'MARENGO', name='embeddingmodel'), nullable=True),
    sa.Column('indexed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('processing_error', sa.Text(), nullable=True),
    sa.Column('summary', sa.Text(), nullable=True),
    sa.Column('risk_score', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_videos_id'), 'videos', ['id'], unique=False)
    op.create_index(op.f('ix_videos_video_id'), 'videos', ['video_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_videos_video_id'), table_name='videos')
    op.drop_index(op.f('ix_videos_id'), table_name='videos')
    op.drop_table('videos')
    op.drop_index(op.f('ix_search_ratings_video_id'), table_name='search_ratings')
    op.drop_index(op.f('ix_search_ratings_query_hash'), table_name='search_ratings')
    op.drop_index(op.f('ix_search_ratings_id'), table_name='search_ratings')
    op.drop_table('search_ratings')
    op.drop_index(op.f('ix_processing_jobs_video_id'), table_name='processing_jobs')
    op.drop_index(op.f('ix_processing_jobs_job_id'), table_name='processing_jobs')
    op.drop_index(op.f('ix_processing_jobs_id'), table_name='processing_jobs')
    op.drop_table('processing_jobs')
    # ### end Alembic commands ###
