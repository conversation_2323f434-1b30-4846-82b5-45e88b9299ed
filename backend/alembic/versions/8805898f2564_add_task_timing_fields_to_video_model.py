"""Add task timing fields to video model

Revision ID: 8805898f2564
Revises: 9c95477c6b38
Create Date: 2025-10-31 11:16:29.208383

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8805898f2564'
down_revision = '9c95477c6b38'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('videos', sa.Column('upload_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('upload_completed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('embedding_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('embedding_completed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('summary_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('videos', sa.Column('summary_completed_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('videos', 'summary_completed_at')
    op.drop_column('videos', 'summary_started_at')
    op.drop_column('videos', 'embedding_completed_at')
    op.drop_column('videos', 'embedding_started_at')
    op.drop_column('videos', 'upload_completed_at')
    op.drop_column('videos', 'upload_started_at')
    # ### end Alembic commands ###
