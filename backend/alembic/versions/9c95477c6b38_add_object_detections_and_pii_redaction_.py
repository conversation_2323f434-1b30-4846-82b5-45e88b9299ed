"""Add object_detections and pii_redaction fields

Revision ID: 9c95477c6b38
Revises: f697fefd718b
Create Date: 2025-10-31 09:17:52.346745

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9c95477c6b38'
down_revision = 'f697fefd718b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('videos', sa.Column('object_detections', sa.JSON(), nullable=True))
    op.add_column('videos', sa.Column('pii_redacted', sa.<PERSON>(), nullable=True))
    op.add_column('videos', sa.Column('pii_redacted_s3_key', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('videos', 'pii_redacted_s3_key')
    op.drop_column('videos', 'pii_redacted')
    op.drop_column('videos', 'object_detections')
    # ### end Alembic commands ###
