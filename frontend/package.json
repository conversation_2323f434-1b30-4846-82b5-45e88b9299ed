{"name": "samsara-video-search-frontend", "version": "2.0.0", "private": true, "dependencies": {"@tanstack/react-query": "^5.17.19", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "axios": "^1.6.5", "lucide-react": "^0.303.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-markdown": "^10.1.0", "react-router-dom": "^6.21.1", "react-scripts": "5.0.1", "recharts": "^2.10.3", "socket.io-client": "^4.6.1", "typescript": "^4.9.5", "web-vitals": "^3.5.1", "zustand": "^4.4.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.11", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0"}}