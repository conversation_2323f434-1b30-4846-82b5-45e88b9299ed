import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>U<PERSON>, Clock, Target, Zap, CheckCircle, AlertCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Layout } from '../components/common/Layout';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import api from '../services/api';

interface ModelAccuracy {
  model: string;
  accuracy_metrics: {
    total_searches: number;
    average_rating: number;
    average_similarity: number;
    satisfaction_rate: number;
    poor_performance_rate: number;
  };
  rating_distribution: Record<string, number>;
  recent_performance: {
    searches_last_7_days: number;
    avg_rating_last_7_days: number;
  };
}

interface ProcessingPerformance {
  processing_performance: {
    upload: { count: number; avg: number; min: number; max: number; median: number };
    nova_embedding: { count: number; avg: number; min: number; max: number; median: number };
    marengo_embedding: { count: number; avg: number; min: number; max: number; median: number };
    summary_generation: { count: number; avg: number; min: number; max: number; median: number };
    total_processing: { count: number; avg: number; min: number; max: number; median: number };
  };
  job_statistics: Record<string, number>;
  recent_metrics: {
    videos_last_7_days: number;
    successfully_indexed: number;
    success_rate_percent: number;
  };
  efficiency_insights: {
    fastest_upload: number;
    fastest_embedding: number;
    avg_total_processing: number;
  };
}

export const EnhancedAnalyticsPage: React.FC = () => {
  const [modelAccuracy, setModelAccuracy] = useState<{ model_accuracy: ModelAccuracy[] } | null>(null);
  const [processingPerformance, setProcessingPerformance] = useState<ProcessingPerformance | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const [accuracyResponse, performanceResponse] = await Promise.all([
          api.get('/analytics/model-accuracy'),
          api.get('/analytics/processing-performance')
        ]);
        
        setModelAccuracy(accuracyResponse.data);
        setProcessingPerformance(performanceResponse.data);
      } catch (error) {
        console.error('Error fetching enhanced analytics:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchAnalytics();
  }, []);

  if (loading) {
    return (
      <Layout>
        <div className="text-center py-12">
          <LoadingSpinner text="Loading enhanced analytics..." />
        </div>
      </Layout>
    );
  }

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    if (seconds < 3600) return `${(seconds / 60).toFixed(1)}m`;
    return `${(seconds / 3600).toFixed(1)}h`;
  };

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Model Accuracy Analytics</h1>
            <p className="mt-2 text-sm text-gray-700">
              Detailed performance metrics and accuracy insights for your video search models
            </p>
          </div>
        </div>

        {/* Model Accuracy Overview */}
        {modelAccuracy && (
          <div className="mt-8">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Model Performance Comparison</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {modelAccuracy.model_accuracy.map((model) => (
                <div key={model.model} className="bg-white shadow rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {model.model === 'nova-premier' ? 'Nova Premier' : 'TwelveLabs Marengo'}
                    </h3>
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${
                            i < Math.round(model.accuracy_metrics.average_rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                      <span className="ml-2 text-sm font-medium text-gray-700">
                        {model.accuracy_metrics.average_rating.toFixed(2)}
                      </span>
                    </div>
                  </div>

                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {model.accuracy_metrics.satisfaction_rate}%
                      </div>
                      <div className="text-xs text-green-700">Satisfaction Rate</div>
                      <div className="text-xs text-gray-500">(4-5 star ratings)</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {model.accuracy_metrics.average_similarity.toFixed(3)}
                      </div>
                      <div className="text-xs text-blue-700">Avg Similarity</div>
                      <div className="text-xs text-gray-500">Search relevance</div>
                    </div>
                  </div>

                  {/* Search Volume */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Total Searches: {model.accuracy_metrics.total_searches}</span>
                    <span>Last 7 days: {model.recent_performance.searches_last_7_days}</span>
                  </div>

                  {/* Performance Indicators */}
                  <div className="flex items-center gap-4 text-xs">
                    <div className="flex items-center gap-1">
                      <CheckCircle className="w-3 h-3 text-green-500" />
                      <span className="text-green-700">
                        {model.accuracy_metrics.satisfaction_rate}% satisfied
                      </span>
                    </div>
                    {model.accuracy_metrics.poor_performance_rate > 0 && (
                      <div className="flex items-center gap-1">
                        <AlertCircle className="w-3 h-3 text-red-500" />
                        <span className="text-red-700">
                          {model.accuracy_metrics.poor_performance_rate}% poor
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Processing Performance */}
        {processingPerformance && (
          <div className="mt-8">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Processing Performance</h2>
            
            {/* Key Performance Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white shadow rounded-lg p-4">
                <div className="flex items-center">
                  <Clock className="h-6 w-6 text-blue-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500">Avg Upload Time</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatTime(processingPerformance.processing_performance.upload.avg)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white shadow rounded-lg p-4">
                <div className="flex items-center">
                  <Zap className="h-6 w-6 text-yellow-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500">Avg Embedding Time</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatTime(Math.max(
                        processingPerformance.processing_performance.nova_embedding.avg,
                        processingPerformance.processing_performance.marengo_embedding.avg
                      ))}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white shadow rounded-lg p-4">
                <div className="flex items-center">
                  <Target className="h-6 w-6 text-green-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500">Success Rate</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {processingPerformance.recent_metrics.success_rate_percent}%
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white shadow rounded-lg p-4">
                <div className="flex items-center">
                  <TrendingUp className="h-6 w-6 text-purple-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500">Total Processing</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatTime(processingPerformance.processing_performance.total_processing.avg)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Detailed Processing Times */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Processing Time Breakdown</h3>
              <div className="space-y-4">
                {Object.entries(processingPerformance.processing_performance).map(([key, stats]) => {
                  if (stats.count === 0) return null;
                  
                  const displayName = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                  
                  return (
                    <div key={key} className="border-b border-gray-200 pb-4 last:border-0">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-900">{displayName}</span>
                        <span className="text-sm text-gray-500">{stats.count} samples</span>
                      </div>
                      <div className="grid grid-cols-4 gap-4 text-xs">
                        <div>
                          <span className="text-gray-500">Avg: </span>
                          <span className="font-medium">{formatTime(stats.avg)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Min: </span>
                          <span className="font-medium text-green-600">{formatTime(stats.min)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Max: </span>
                          <span className="font-medium text-red-600">{formatTime(stats.max)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Median: </span>
                          <span className="font-medium">{formatTime(stats.median)}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {(!modelAccuracy || !processingPerformance) && (
          <div className="mt-8 text-center py-12 bg-white shadow rounded-lg">
            <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No analytics data available</h3>
            <p className="mt-1 text-sm text-gray-500">
              Upload videos and perform searches to see detailed analytics
            </p>
          </div>
        )}
      </div>
    </Layout>
  );
};
