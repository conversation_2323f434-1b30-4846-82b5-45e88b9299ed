import { CheckCircle, Download, Eye, Search, Shield, Star } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { AlertDialog } from '../components/AlertDialog';
import { Layout } from '../components/common/Layout';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { searchService } from '../services/search';
import type { SearchResult } from '../types';

// Collapsible Summary Component with On-Demand Analysis
const CollapsibleSummary: React.FC<{ result: SearchResult }> = ({ result }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [detailedSummary, setDetailedSummary] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!result.safety_report) return null;

  const handleExpand = async () => {
    if (!isExpanded) {
      setIsExpanded(true);

      // Load detailed analysis if not already loaded and analysis is available
      if (!detailedSummary && result.safety_report?.analysis_available && result.safety_report?.clip_s3_key) {
        setIsLoading(true);
        setError(null);

        try {
          const response = await searchService.analyzeClip(
            result.safety_report.clip_s3_key,
            result.video_id,
            result.start_sec,
            result.duration
          );
          setDetailedSummary(response.summary);
        } catch (err) {
          console.error('Failed to load detailed analysis:', err);
          setError('Failed to load detailed analysis. Please try again.');
        } finally {
          setIsLoading(false);
        }
      }
    } else {
      setIsExpanded(false);
    }
  };

  const displaySummary = detailedSummary || result.safety_report.summary;

  return (
    <div className="mt-6 bg-blue-50 rounded-lg border border-blue-200 overflow-hidden">
      <button
        onClick={handleExpand}
        className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-blue-100 transition-colors"
      >
        <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
          <Shield className="w-4 h-4 text-blue-600" />
          Clip Summary {result.safety_report?.analysis_available && !detailedSummary && '(Click for AI Analysis)'}
        </h4>
        {isExpanded ? (
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        ) : (
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </button>

      {isExpanded && (
        <div className="px-4 pb-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              <LoadingSpinner text="Generating AI analysis..." />
            </div>
          ) : error ? (
            <div className="text-sm text-red-600 py-2">
              {error}
            </div>
          ) : (
            <div className="text-sm text-gray-700 whitespace-pre-wrap">
              {displaySummary}
            </div>
          )}
          <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
            <span>Model: {result.safety_report.model}</span>
            {detailedSummary && (
              <span className="text-green-600 font-medium">✓ AI Analysis Complete</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Video Player Component with Tab View
const VideoPlayer: React.FC<{ result: SearchResult }> = ({ result }) => {
  // Show processed by default if available, otherwise show original
  const hasProcessedVideo = Boolean(result.processed_clip_url);
  const [activeTab, setActiveTab] = useState<'processed' | 'original'>(
    hasProcessedVideo ? 'processed' : 'original'
  );

  // Determine which video to show based on active tab
  const currentVideoUrl = activeTab === 'processed' && result.processed_clip_url
    ? result.processed_clip_url
    : result.clip_url;

  // Debug logging
  console.log('VideoPlayer Debug:', {
    hasProcessedVideo,
    processed_clip_url: result.processed_clip_url,
    processing_status: result.processing_status,
    activeTab,
    currentVideoUrl
  });

  return (
    <div className="space-y-4">
      {/* Tab Controls */}
      {hasProcessedVideo && (
        <div className="flex border-b border-gray-200 mb-4">
          <button
            onClick={() => setActiveTab('processed')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'processed'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Processed (YOLOv8 + PII)
            </div>
          </button>
          <button
            onClick={() => setActiveTab('original')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'original'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Original
            </div>
          </button>
        </div>
      )}

      {/* Video Info Header */}
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
          {activeTab === 'processed' ? (
            <>
              <Shield className="w-4 h-4 text-green-600" />
              Processed Video (Object Detection + PII Blur)
            </>
          ) : (
            <>
              <Eye className="w-4 h-4" />
              Original Video
            </>
          )}
        </h4>

        {/* Download Button */}
        {currentVideoUrl && (
          <a
            href={currentVideoUrl}
            download
            className="text-primary-600 hover:text-primary-700 text-xs flex items-center gap-1"
          >
            <Download className="w-3 h-3" />
            Download
          </a>
        )}
      </div>

      {/* Video Player */}
      <div className="relative bg-gray-900 rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
        {currentVideoUrl ? (
          <>
            <video
              key={currentVideoUrl} // Force re-render when URL changes
              controls
              className="w-full h-full"
              preload="metadata"
              src={currentVideoUrl}
            >
              Your browser does not support the video tag.
            </video>

            {/* Status Indicators */}
            {activeTab === 'processed' && hasProcessedVideo && (
              <div className="absolute top-2 right-2 bg-green-600 bg-opacity-90 px-3 py-1.5 rounded-md text-xs text-white font-medium flex items-center gap-1.5 shadow-lg">
                <CheckCircle className="w-4 h-4" />
                YOLOv8 Enhanced • PII Protected
              </div>
            )}

            {result.processing_status === 'failed' && (
              <div className="absolute top-2 right-2 bg-red-600 bg-opacity-90 px-3 py-1.5 rounded-md text-xs text-white font-medium flex items-center gap-1.5 shadow-lg">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                Processing Failed
              </div>
            )}
          </>
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            <LoadingSpinner text="Extracting clip..." />
          </div>
        )}
      </div>

      {/* Processing Status */}
      {result.processing_status === 'processing' && result.processing_task_id && (
        <p className="text-xs text-gray-500 text-center">
          Task ID: {result.processing_task_id.substring(0, 8)}...
        </p>
      )}

      {/* Processing Info */}
      {!hasProcessedVideo && result.clip_url && (
        <p className="text-xs text-gray-500 text-center">
          Processed version with object detection and PII blurring will be available shortly
        </p>
      )}
    </div>
  );
};

export const SearchPage: React.FC = () => {
  const [query, setQuery] = useState('');
  const [model, setModel] = useState<'nova-premier' | 'marengo'>('nova-premier');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [presets, setPresets] = useState<any[]>([]);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertConfig, setAlertConfig] = useState({ title: '', message: '', type: 'info' as 'success' | 'error' | 'warning' | 'info' });

  useEffect(() => {
    const fetchPresets = async () => {
      try {
        const data = await searchService.getPresets();
        setPresets(data.presets);
      } catch (error) {
        console.error('Error fetching presets:', error);
      }
    };
    fetchPresets();
  }, []);

  const handleSearch = async () => {
    if (!query.trim()) return;
    
    setLoading(true);
    try {
      const response = await searchService.search({
        query,
        top_k: 10,
        model
      });
      setResults(response.results);
    } catch (error: any) {
      console.error('Search error:', error);
      setAlertConfig({
        title: 'Search Failed',
        message: error.response?.data?.detail || 'An error occurred while searching. Please try again.',
        type: 'error'
      });
      setAlertOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const handlePresetClick = (presetQuery: string) => {
    setQuery(presetQuery);
  };

  const handleRate = async (result: SearchResult, rating: number) => {
    try {
      await searchService.rateResult({
        query,
        video_id: result.video_id,
        segment_id: result.segment_id,
        rating,
        similarity_score: result.similarity_score,
        model_type: model
      });
      setAlertConfig({
        title: 'Success',
        message: 'Rating submitted successfully!',
        type: 'success'
      });
      setAlertOpen(true);
    } catch (error) {
      console.error('Rating error:', error);
    }
  };

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Search Videos</h1>
            <p className="mt-2 text-sm text-gray-700">
              Semantic search across indexed videos using AI embeddings
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mt-8">
          <div className="flex gap-4">
            <div className="flex-1">
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                placeholder="Describe what you're looking for..."
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm px-4 py-3"
              />
            </div>
            <select
              value={model}
              onChange={(e) => setModel(e.target.value as any)}
              className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm px-4"
            >
              <option value="nova-premier">Nova Premier</option>
              <option value="marengo">TwelveLabs Marengo</option>
            </select>
            <button
              onClick={handleSearch}
              disabled={loading || !query.trim()}
              className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              <Search className="w-4 h-4 mr-2" />
              Search
            </button>
          </div>
        </div>

        {/* Preset Queries */}
        <div className="mt-6">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Searches:</h3>
          <div className="flex flex-wrap gap-2">
            {presets.map((preset) => (
              <button
                key={preset.id}
                onClick={() => handlePresetClick(preset.query)}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                {preset.label}
              </button>
            ))}
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="mt-8 text-center py-12">
            <LoadingSpinner text="Searching videos..." />
          </div>
        )}

        {/* Results */}
        {!loading && results.length > 0 && (
          <div className="mt-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Found {results.length} results for "{query}"
            </h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {results.map((result, index) => (
                <div
                  key={`${result.video_id}-${result.segment_id}`}
                  className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200"
                >
                  {/* Header */}
                  <div className="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-bold text-sm">#{index + 1}</span>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            Video: {result.video_id}
                          </h3>
                          <p className="text-sm text-gray-600">
                            Segment #{result.segment_id} • {Math.floor(result.duration)}s clip at {Math.floor(result.start_sec / 60)}:{(result.start_sec % 60).toFixed(0).padStart(2, '0')}
                          </p>
                        </div>
                      </div>

                    </div>
                  </div>

                  {/* Video Container */}
                  <div className="p-6">
                    <VideoPlayer result={result} />

                    {/* Collapsible Clip Summary */}
                    <CollapsibleSummary result={result} />

                    {/* Metadata & Actions */}
                    <div className="mt-6 flex items-center justify-between pt-4 border-t border-gray-200">
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>{result.timestamp}</span>
                        {result.metadata.video_resolution && (
                          <span>• {result.metadata.video_resolution}</span>
                        )}
                        {result.metadata.video_duration && (
                          <span>• {Math.floor(result.metadata.video_duration / 60)}:{(result.metadata.video_duration % 60).toFixed(0).padStart(2, '0')} total</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500 mr-2">Rate this result:</span>
                        {[1, 2, 3, 4, 5].map((rating) => (
                          <button
                            key={rating}
                            onClick={() => handleRate(result, rating)}
                            className="text-gray-300 hover:text-yellow-400 focus:outline-none transition-colors"
                            title={`Rate ${rating} stars`}
                          >
                            <Star className="w-5 h-5" fill="currentColor" />
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* No Results */}
        {!loading && results.length === 0 && query && (
          <div className="mt-8 text-center py-12">
            <Search className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No results found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try a different search query or check if videos are indexed
            </p>
          </div>
        )}

        {/* Empty State */}
        {!loading && results.length === 0 && !query && (
          <div className="mt-8 text-center py-12">
            <Search className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Start searching</h3>
            <p className="mt-1 text-sm text-gray-500">
              Enter a query or select a preset to search through indexed videos
            </p>
          </div>
        )}
      </div>

      {/* Alert Dialog */}
      <AlertDialog
        isOpen={alertOpen}
        onClose={() => setAlertOpen(false)}
        title={alertConfig.title}
        message={alertConfig.message}
        type={alertConfig.type}
      />
    </Layout>
  );
};
