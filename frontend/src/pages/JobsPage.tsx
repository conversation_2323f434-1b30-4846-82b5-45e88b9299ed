import { Activity, AlertCircle, CheckCircle, Clock, RefreshCw, Timer, XCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Layout } from '../components/common/Layout';
import { useVideoTasks } from '../hooks/useJobs';
import { useWebSocket } from '../hooks/useWebSocket';

// Elapsed Time Component
const ElapsedTime: React.FC<{ startTime: string }> = ({ startTime }) => {
  const [elapsed, setElapsed] = useState(0);
  
  useEffect(() => {
    const calculateElapsed = () => {
      const start = new Date(startTime).getTime();
      const now = new Date().getTime();
      setElapsed(Math.floor((now - start) / 1000));
    };
    
    calculateElapsed();
    const interval = setInterval(calculateElapsed, 1000);
    
    return () => clearInterval(interval);
  }, [startTime]);
  
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };
  
  return (
    <div className="flex items-center text-xs text-gray-500">
      <Timer className="w-3 h-3 mr-1" />
      {formatTime(elapsed)}
    </div>
  );
};

const JobsPage: React.FC = () => {
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Use React Query for data fetching and caching
  const { 
    data: videoTasks = [], 
    isLoading: initialLoad, 
    error,
    refetchTasks 
  } = useVideoTasks(10);

  // Use WebSocket for real-time updates
  const { isConnected: wsConnected } = useWebSocket();

  const handleManualRefresh = async () => {
    setRefreshing(true);
    try {
      await refetchTasks();
    } finally {
      setRefreshing(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
      case 'processing':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'indexed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'uploading':
        return 'bg-yellow-100 text-yellow-800';
      case 'uploaded':
        return 'bg-purple-100 text-purple-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTaskStatus = (task: any) => {
    if (task.progress?.percent === 100 || task.status === 'completed') {
      return 'completed';
    } else if (task.progress?.percent > 0 || task.status === 'in_progress' || task.status === 'processing') {
      return 'in_progress';
    } else if (task.status === 'failed') {
      return 'failed';
    } else {
      return 'pending';
    }
  };

  const renderTaskCell = (videoTask: any, taskType: string) => {
    const task = videoTask.tasks.find((t: any) => t.type === taskType);
    
    if (!task) {
      return (
        <td className="px-6 py-4 text-center">
          <div className="flex items-center justify-center">
            <AlertCircle className="w-5 h-5 text-gray-300" />
          </div>
        </td>
      );
    }

    const status = getTaskStatus(task);
    const icon = getStatusIcon(status);
    
    return (
      <td className="px-6 py-4 text-center">
        <div className="flex flex-col items-center space-y-1">
          <div className="flex items-center justify-center">
            {icon}
          </div>
          
          {task.progress && (
            <div className="text-xs text-gray-500">
              {task.progress.percent}%
            </div>
          )}
          
          {task.started_at && status === 'in_progress' && (
            <ElapsedTime startTime={task.started_at} />
          )}
          
          {task.duration_seconds && status === 'completed' && (
            <div className="text-xs text-gray-500">
              {task.duration_seconds}s
            </div>
          )}
          
          {task.chunk_count && (
            <div className="text-xs text-gray-500">
              {task.chunk_count} chunks
            </div>
          )}
          
          {task.clip_count && (
            <div className="text-xs text-gray-500">
              {task.clip_count} clips
            </div>
          )}
        </div>
      </td>
    );
  };

  if (error) {
    return (
      <Layout>
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <XCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading jobs</h3>
                <div className="mt-2 text-sm text-red-700">
                  {error instanceof Error ? error.message : 'An unknown error occurred'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Activity className="w-8 h-8 mr-3 text-blue-600" />
              Processing Jobs
            </h1>
            <p className="text-gray-600 mt-1">Monitor video processing tasks and their progress</p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* WebSocket Status */}
            <div className="flex items-center text-sm">
              {wsConnected ? (
                <>
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  <span className="text-green-600">Live Updates</span>
                </>
              ) : (
                <>
                  <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                  <span className="text-gray-500">Polling Mode</span>
                </>
              )}
            </div>

            {/* Auto Refresh Toggle */}
            <label className="flex items-center text-sm">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="mr-2"
              />
              Auto-refresh
            </label>

            {/* Manual Refresh */}
            <button
              onClick={handleManualRefresh}
              disabled={refreshing}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {/* Loading State */}
        {initialLoad && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading jobs...</span>
          </div>
        )}

        {/* Empty State */}
        {!initialLoad && videoTasks.length === 0 && (
          <div className="text-center py-12">
            <Activity className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No processing jobs</h3>
            <p className="mt-1 text-sm text-gray-500">Upload a video to see processing jobs here.</p>
          </div>
        )}

        {/* Jobs Table */}
        {!initialLoad && videoTasks.length > 0 && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Recent Video Processing Tasks</h2>
                  <p className="text-sm text-gray-500 mt-1">
                    Showing task status for last 10 videos. Updates automatically every 5 seconds.
                  </p>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded font-medium">PARALLEL</span>
                  <span className="text-gray-500">= Upload → Dual Embeddings → Summary → Clips</span>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Video
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Upload
                      <div className="text-xs font-normal text-gray-400 mt-1">Step 1</div>
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nova Embedding
                      <div className="text-xs font-normal text-gray-400 mt-1">Step 2a</div>
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Marengo Embedding
                      <div className="text-xs font-normal text-gray-400 mt-1">Step 2b</div>
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Summary
                      <div className="text-xs font-normal text-gray-400 mt-1">Step 3</div>
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Clip Generation
                      <div className="text-xs font-normal text-gray-400 mt-1">Step 4 (Final)</div>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {videoTasks.map((videoTask) => (
                    <tr key={videoTask.video_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                          {videoTask.filename}
                        </div>
                        <div className="text-xs text-gray-500">{videoTask.video_id}</div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(videoTask.video_status)}`}>
                          {videoTask.video_status}
                        </span>
                      </td>
                      {['upload', 'nova_embedding', 'marengo_embedding', 'summary', 'clip_generation'].map((taskType) => 
                        renderTaskCell(videoTask, taskType)
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default JobsPage;
