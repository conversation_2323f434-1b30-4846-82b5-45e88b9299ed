import React, { useState, useEffect, useCallback } from 'react';
import { Activity, Clock, CheckCircle, XCircle, AlertCircle, RefreshCw, Timer } from 'lucide-react';
import api from '../services/api';
import { Layout } from '../components/common/Layout';

// Elapsed Time Component
const ElapsedTime: React.FC<{ startTime: string }> = ({ startTime }) => {
  const [elapsed, setElapsed] = useState(0);
  
  useEffect(() => {
    const calculateElapsed = () => {
      const start = new Date(startTime).getTime();
      const now = new Date().getTime();
      setElapsed(Math.floor((now - start) / 1000));
    };
    
    calculateElapsed();
    const interval = setInterval(calculateElapsed, 1000);
    
    return () => clearInterval(interval);
  }, [startTime]);
  
  const minutes = Math.floor(elapsed / 60);
  const seconds = elapsed % 60;
  
  return (
    <span className="text-xs text-gray-600 flex items-center gap-1">
      <Timer className="w-3 h-3" />
      {minutes}:{seconds.toString().padStart(2, '0')} elapsed
    </span>
  );
};

interface Task {
  type: string;
  status: string;
  has_result?: boolean;
  arn?: string;
  task_id?: string;
  started_at?: string;
  completed_at?: string;
  duration_seconds?: number;
  chunk_count?: number;
  clip_count?: number;
  nova_completed?: string;
  marengo_completed?: string;
  progress?: {
    percent: number;
    status: string;
    current: number;
    total: number;
  };
}

interface VideoTask {
  video_id: string;
  video_status: string;
  filename: string;
  tasks: Array<Task>;
}

export const JobsPage: React.FC = () => {
  const [videoTasks, setVideoTasks] = useState<VideoTask[]>([]);
  const [initialLoad, setInitialLoad] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchRecentVideoTasks = useCallback(async () => {
    try {
      // Get recent videos (baseURL already includes /api)
      const videosResponse = await api.get('/videos', { params: { limit: 10 } });
      const videos = Array.isArray(videosResponse.data) ? videosResponse.data : [];
      
      if (videos.length === 0) {
        setVideoTasks([]);
        return;
      }
      
      // Get task status for each video
      const taskPromises = videos.map(async (video: any) => {
        try {
          const taskData = await api.get(`/tasks/video/${video.video_id}`);
          return taskData.data;
        } catch (e) {
          console.error('Error fetching tasks for video', video.video_id, ':', e);
          return null;
        }
      });
      
      const tasks = await Promise.all(taskPromises);
      const validTasks = tasks.filter(Boolean);
      
      // Only update state if data actually changed
      setVideoTasks(prevTasks => {
        const hasChanged = JSON.stringify(prevTasks) !== JSON.stringify(validTasks);
        return hasChanged ? validTasks : prevTasks;
      });
    } catch (error) {
      console.error('Error fetching video tasks:', error);
      setVideoTasks([]);
    } finally {
      setInitialLoad(false);
    }
  }, []);

  const handleManualRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchRecentVideoTasks();
    } finally {
      setRefreshing(false);
    }
  }, [fetchRecentVideoTasks]);

  // Initial fetch
  useEffect(() => {
    fetchRecentVideoTasks();
  }, [fetchRecentVideoTasks]);

  // Auto-refresh every 5 seconds
  useEffect(() => {
    const pollInterval = setInterval(() => {
      if (autoRefresh) {
        fetchRecentVideoTasks();
      }
    }, 5000);

    return () => {
      clearInterval(pollInterval);
    };
  }, [autoRefresh, fetchRecentVideoTasks]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
      case 'processing':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'indexed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Don't block rendering - show UI immediately and load data in background

  return (
    <Layout>
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Activity className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">Jobs & Tasks Monitor</h1>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Auto-refresh: 5s
          </span>
        </div>
        
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm text-gray-600">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            Auto-refresh enabled
          </label>
          
          <button
            onClick={handleManualRefresh}
            disabled={refreshing}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Video Tasks Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Recent Video Processing Tasks</h2>
              <p className="text-sm text-gray-500 mt-1">
                Showing task status for last 10 videos. Auto-refreshes every 5 seconds.
              </p>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded font-medium">SEQUENTIAL</span>
              <span className="text-gray-500">= Upload → Dual Embeddings → Summary → Clips</span>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Video
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div>Upload</div>
                  <div className="text-xs font-normal text-gray-400 mt-1">Step 1</div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div className="flex items-center gap-1">
                    <span>Nova Embeddings</span>
                    <span className="px-1.5 py-0.5 bg-purple-100 text-purple-600 rounded text-[10px]">∥</span>
                  </div>
                  <div className="text-xs font-normal text-gray-400 mt-1">Step 2a</div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div className="flex items-center gap-1">
                    <span>Marengo Embeddings</span>
                    <span className="px-1.5 py-0.5 bg-purple-100 text-purple-600 rounded text-[10px]">∥</span>
                  </div>
                  <div className="text-xs font-normal text-gray-400 mt-1">Step 2b</div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div>Summary</div>
                  <div className="text-xs font-normal text-gray-400 mt-1">Step 3</div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div>Clip Generation</div>
                  <div className="text-xs font-normal text-gray-400 mt-1">Step 4 (Final)</div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {videoTasks.map((videoTask) => (
                <tr key={videoTask.video_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                      {videoTask.filename}
                    </div>
                    <div className="text-xs text-gray-500">{videoTask.video_id}</div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(videoTask.video_status)}`}>
                      {videoTask.video_status}
                    </span>
                  </td>
                  {['upload', 'nova_embedding', 'marengo_embedding', 'summary', 'clip_generation'].map((taskType) => {
                    const task = videoTask.tasks.find(t => t.type === taskType);
                    const hasProgress = task?.progress && task.progress.percent > 0 && task.progress.percent < 100;
                    const duration = task?.duration_seconds ? `${Math.round(task.duration_seconds)}s` : null;
                    
                    return (
                      <td key={taskType} className="px-6 py-4">
                        {task?.status === 'running' && task?.started_at ? (
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                              <span className="text-sm text-gray-600">Processing</span>
                            </div>
                            <ElapsedTime startTime={task.started_at} />
                            {task?.started_at && (
                              <p className="text-xs text-gray-500">
                                Started: {new Date(task.started_at).toLocaleTimeString()}
                              </p>
                            )}
                          </div>
                        ) : hasProgress && task.progress && task.status !== 'completed' ? (
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                              <span className="text-sm text-gray-600">Processing</span>
                            </div>
                            <p className="text-xs text-gray-500 truncate">
                              {task.progress.status || `${task.progress.percent}%`}
                            </p>
                          </div>
                        ) : (
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(task?.status || 'pending')}
                              <span className="text-sm font-medium text-gray-700 capitalize">
                                {task?.status || 'pending'}
                              </span>
                            </div>
                            {task?.started_at && (
                              <p className="text-xs text-gray-600">
                                <span className="font-medium">Start:</span> {new Date(task.started_at).toLocaleString()}
                              </p>
                            )}
                            {task?.completed_at && (
                              <p className="text-xs text-gray-600">
                                <span className="font-medium">End:</span> {new Date(task.completed_at).toLocaleString()}
                              </p>
                            )}
                            {duration && (
                              <p className="text-xs text-blue-600 font-medium">
                                ⏱️ {duration}
                              </p>
                            )}
                            {task?.chunk_count && task.chunk_count > 0 && (
                              <p className="text-xs text-green-600 font-medium">
                                📦 {task.chunk_count} chunks
                              </p>
                            )}
                            {task?.clip_count && task.clip_count > 0 && (
                              <p className="text-xs text-green-600 font-medium">
                                🎬 {task.clip_count} clips
                              </p>
                            )}
                          </div>
                        )}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>

          {initialLoad && videoTasks.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              <RefreshCw className="w-12 h-12 mx-auto mb-4 opacity-50 animate-spin" />
              <p className="text-lg font-medium">Loading tasks...</p>
              <p className="text-sm mt-2">Please wait while we fetch video processing tasks</p>
            </div>
          )}

          {!initialLoad && videoTasks.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No recent tasks found</p>
              <p className="text-sm mt-2">Upload a video to see processing tasks here</p>
              <button
                onClick={handleManualRefresh}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Refresh
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
    </Layout>
  );
};
