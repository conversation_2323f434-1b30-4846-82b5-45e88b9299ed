import React, { useEffect, useState, useCallback } from 'react';
import { Layout } from '../components/common/Layout';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { SummaryDrawer } from '../components/SummaryDrawer';
import { VideoPlayerDrawer } from '../components/VideoPlayerDrawer';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { videosService } from '../services/videos';
import { websocketService, WebSocketMessage } from '../services/websocket';
import { Video, Trash2, RefreshCw, FileText, Filter, Play, Eye, Wifi, WifiOff } from 'lucide-react';
import type { Video as VideoType } from '../types';

export const VideosPage: React.FC = () => {
  const [videos, setVideos] = useState<VideoType[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [modelFilter, setModelFilter] = useState<string>('');
  const [summaryDrawerOpen, setSummaryDrawerOpen] = useState(false);
  const [selectedSummary, setSelectedSummary] = useState<string>('');
  const [selectedVideoTitle, setSelectedVideoTitle] = useState<string>('');
  const [videoPlayerOpen, setVideoPlayerOpen] = useState(false);
  const [selectedVideoUrl, setSelectedVideoUrl] = useState<string>('');
  const [selectedVideoId, setSelectedVideoId] = useState<string>('');
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [videoToDelete, setVideoToDelete] = useState<string>('');
  const [wsConnected, setWsConnected] = useState(false);

  const fetchVideos = useCallback(async () => {
    setLoading(true);
    try {
      const data = await videosService.listVideos({
        status_filter: statusFilter || undefined,
        model_filter: modelFilter || undefined,
      });
      setVideos(data);
    } catch (error) {
      console.error('Error fetching videos:', error);
    } finally {
      setLoading(false);
    }
  }, [statusFilter, modelFilter]);

  // Initial fetch
  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  // WebSocket connection for real-time updates
  useEffect(() => {
    console.log('🔌 Connecting to WebSocket...');
    websocketService.connect();

    // Handle WebSocket messages
    const unsubscribe = websocketService.onMessage((message: WebSocketMessage) => {
      console.log('📨 Received WebSocket message:', message);

      if (message.type === 'connection') {
        setWsConnected(true);
        console.log('✅ WebSocket connected');
      } else if (message.type === 'video_update' && message.video_id) {
        // Update specific video status in real-time
        console.log(`🔄 Updating video ${message.video_id} status to ${message.status}`);
        setVideos(prevVideos =>
          prevVideos.map(video =>
            video.video_id === message.video_id
              ? { ...video, status: (message.status || video.status) as VideoType['status'] }
              : video
          )
        );
      } else if (message.type === 'job_update' && message.video_id) {
        // Handle job progress updates
        console.log(`📊 Job update for video ${message.video_id}: ${message.progress}%`);
        // Optionally update UI with progress
      }
    });

    // Fallback polling every 30 seconds (less frequent than before)
    // This ensures we don't miss updates if WebSocket fails
    const pollInterval = setInterval(() => {
      if (!websocketService.isConnected()) {
        console.log('⚠️ WebSocket disconnected, using fallback polling');
        fetchVideos();
      }
    }, 30000);

    // Cleanup on unmount
    return () => {
      console.log('🔌 Disconnecting WebSocket...');
      unsubscribe();
      websocketService.disconnect();
      clearInterval(pollInterval);
      setWsConnected(false);
    };
  }, [fetchVideos]);

  const handleDelete = (videoId: string) => {
    setVideoToDelete(videoId);
    setConfirmDialogOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await videosService.deleteVideo(videoToDelete);
      setVideos(videos.filter(v => v.video_id !== videoToDelete));
      // Success - video deleted
    } catch (error) {
      console.error('Delete error:', error);
      // Error handled
    }
  };

  const handleReindex = async (videoId: string) => {
    try {
      await videosService.indexVideo(videoId);
      // Reindexing started successfully
      fetchVideos();
    } catch (error) {
      console.error('Reindex error:', error);
      // Failed to start reindexing
    }
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      indexed: 'bg-green-100 text-green-800',
      processing: 'bg-yellow-100 text-yellow-800',
      uploading: 'bg-blue-100 text-blue-800',
      uploaded: 'bg-indigo-100 text-indigo-800',
      failed: 'bg-red-100 text-red-800',
    };
    return styles[status as keyof typeof styles] || 'bg-gray-100 text-gray-800';
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const mb = bytes / (1024 * 1024);
    return mb < 1024 ? `${mb.toFixed(2)} MB` : `${(mb / 1024).toFixed(2)} GB`;
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="sm:flex sm:items-center sm:justify-between">
          <div className="sm:flex-auto">
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-semibold text-gray-900">Videos</h1>
              {wsConnected ? (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <Wifi className="w-3 h-3 mr-1" />
                  Live
                </span>
              ) : (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  <WifiOff className="w-3 h-3 mr-1" />
                  Polling
                </span>
              )}
            </div>
            <p className="mt-2 text-sm text-gray-700">
              Manage all uploaded and indexed videos
              {wsConnected && <span className="text-green-600"> • Real-time updates enabled</span>}
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              onClick={fetchVideos}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mt-6 flex gap-4">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              <option value="">All Status</option>
              <option value="uploading">Uploading</option>
              <option value="uploaded">Uploaded</option>
              <option value="processing">Processing</option>
              <option value="indexed">Indexed</option>
              <option value="failed">Failed</option>
            </select>
          </div>
          <div>
            <select
              value={modelFilter}
              onChange={(e) => setModelFilter(e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              <option value="">All Models</option>
              <option value="nova-premier">Nova Premier</option>
              <option value="marengo">TwelveLabs Marengo</option>
            </select>
          </div>
        </div>

        {/* Videos List */}
        <div className="mt-8">
          {loading ? (
            <div className="text-center py-12">
              <LoadingSpinner text="Loading videos..." />
            </div>
          ) : videos.length === 0 ? (
            <div className="text-center py-12">
              <Video className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No videos found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {statusFilter || modelFilter
                  ? 'Try adjusting your filters'
                  : 'Upload your first video to get started'}
              </p>
            </div>
          ) : (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {videos.map((video) => (
                  <li key={video.video_id}>
                    <div className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                      <div className="flex items-center justify-between gap-4">
                        {/* Video Thumbnail */}
                        <div className="flex-shrink-0">
                          <button
                            onClick={() => {
                              setSelectedVideoUrl(`http://localhost:8000/api/videos/${video.video_id}/download`);
                              setSelectedVideoTitle(video.filename);
                              setSelectedVideoId(video.video_id);
                              setVideoPlayerOpen(true);
                            }}
                            className="block w-32 h-20 bg-gradient-to-br from-gray-700 to-gray-900 rounded-lg overflow-hidden relative group hover:shadow-lg transition-shadow cursor-pointer"
                          >
                            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all">
                              <Play className="w-10 h-10 text-white group-hover:scale-110 transition-transform" />
                            </div>
                            <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 px-1.5 py-0.5 rounded text-xs text-white font-medium">
                              {video.duration ? formatDuration(video.duration) : '0:00'}
                            </div>
                            <div className="absolute top-1 left-1 bg-primary-600 bg-opacity-90 px-1.5 py-0.5 rounded text-xs text-white font-medium">
                              {video.width && video.height ? `${video.height}p` : 'HD'}
                            </div>
                          </button>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-3">
                            <div>
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {video.filename}
                              </p>
                              <p className="text-xs text-gray-500">
                                ID: {video.video_id}
                              </p>
                            </div>
                          </div>
                          <div className="mt-2 flex items-center gap-4 text-xs text-gray-500">
                            <span>{formatFileSize(video.file_size)}</span>
                            {video.duration && <span>{formatDuration(video.duration)}</span>}
                            {video.width && video.height && (
                              <span>{video.width}x{video.height}</span>
                            )}
                            {video.fps && <span>{video.fps} fps</span>}
                            {video.embedding_model && (
                              <span className="text-primary-600">
                                {video.embedding_model === 'nova-premier' ? 'Nova' : 'Marengo'}
                              </span>
                            )}
                          </div>
                          <p className="mt-1 text-xs text-gray-500">
                            Created: {new Date(video.created_at).toLocaleString()}
                          </p>
                        </div>
                        <div className="ml-4 flex items-center gap-3">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(
                              video.status
                            )}`}
                          >
                            {video.status}
                          </span>
                          <div className="flex gap-2">
                            {(video.status === 'indexed' || video.status === 'uploaded') && (
                              <button
                                onClick={() => {
                                  setSelectedVideoUrl(`http://localhost:8000/api/videos/${video.video_id}/download`);
                                  setSelectedVideoTitle(video.filename);
                                  setSelectedVideoId(video.video_id);
                                  setVideoPlayerOpen(true);
                                }}
                                className="text-green-600 hover:text-green-900"
                                title="View video"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                            )}
                            {video.status === 'uploaded' && (
                              <button
                                onClick={() => handleReindex(video.video_id)}
                                className="text-primary-600 hover:text-primary-900"
                                title="Reindex video"
                              >
                                <RefreshCw className="w-4 h-4" />
                              </button>
                            )}
                            {video.summary && (
                              <button
                                onClick={() => {
                                  setSelectedSummary(video.summary || '');
                                  setSelectedVideoTitle(video.filename);
                                  setSummaryDrawerOpen(true);
                                }}
                                className="text-blue-600 hover:text-blue-900"
                                title="View summary"
                              >
                                <FileText className="w-4 h-4" />
                              </button>
                            )}
                            <button
                              onClick={() => handleDelete(video.video_id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete video"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

      </div>

      {/* Summary Drawer */}
      <SummaryDrawer
        isOpen={summaryDrawerOpen}
        onClose={() => setSummaryDrawerOpen(false)}
        summary={selectedSummary}
        videoTitle={selectedVideoTitle}
      />

      {/* Video Player Drawer */}
      <VideoPlayerDrawer
        isOpen={videoPlayerOpen}
        onClose={() => setVideoPlayerOpen(false)}
        videoUrl={selectedVideoUrl}
        videoTitle={selectedVideoTitle}
        videoId={selectedVideoId}
      />

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        isOpen={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Video"
        message="Are you sure you want to delete this video? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
      />
    </Layout>
  );
};
