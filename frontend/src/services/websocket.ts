const WS_URL = process.env.REACT_APP_WS_URL || 'ws://localhost:8000';

export type WebSocketMessage = {
  type: 'connection' | 'video_update' | 'job_update' | 'upload_progress' | 'pong' | 'error' | 'subscribed' | 'unsubscribed';
  video_id?: string;
  status?: string;
  message?: string;
  progress?: number;
  job_id?: string;
  client_id?: string;
  timestamp?: number;
};

type MessageHandler = (data: WebSocketMessage) => void;

class WebSocketService {
  private socket: WebSocket | null = null;
  private clientId: string;
  private messageHandlers: Set<MessageHandler> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 3000;
  private pingInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.clientId = this.generateClientId();
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  connect() {
    if (this.socket?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return;
    }

    const wsUrl = `${WS_URL}/api/ws/${this.clientId}`;
    console.log('Connecting to WebSocket:', wsUrl);

    try {
      this.socket = new WebSocket(wsUrl);

      this.socket.onopen = () => {
        console.log('✅ WebSocket connected');
        this.reconnectAttempts = 0;

        // Start ping interval to keep connection alive
        this.startPingInterval();
      };

      this.socket.onmessage = (event) => {
        try {
          const data: WebSocketMessage = JSON.parse(event.data);
          console.log('📨 WebSocket message:', data);

          // Notify all handlers
          this.messageHandlers.forEach(handler => {
            try {
              handler(data);
            } catch (error) {
              console.error('Error in message handler:', error);
            }
          });
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.socket.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };

      this.socket.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code, event.reason);
        this.stopPingInterval();

        // Only attempt to reconnect for normal closures or network errors
        // Don't reconnect if server explicitly closed (code 1000) or refused (code 1006 after multiple attempts)
        if (this.reconnectAttempts < this.maxReconnectAttempts && event.code !== 1000) {
          this.reconnectAttempts++;
          console.log(`🔄 Reconnecting... (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
          setTimeout(() => this.connect(), this.reconnectDelay);
        } else {
          if (event.code === 1006) {
            console.warn('⚠️ WebSocket connection refused - running without real-time updates');
          } else {
            console.log('ℹ️ WebSocket disconnected - running without real-time updates');
          }
        }
      };
    } catch (error) {
      console.error('Error creating WebSocket:', error);
    }
  }

  disconnect() {
    this.stopPingInterval();
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.messageHandlers.clear();
  }

  private startPingInterval() {
    this.stopPingInterval();
    this.pingInterval = setInterval(() => {
      this.send({
        type: 'ping' as any,
        timestamp: Date.now()
      });
    }, 30000); // Ping every 30 seconds
  }

  private stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  send(data: any) {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  subscribe(subscription: string) {
    this.send({
      type: 'subscribe',
      subscription,
    });
  }

  unsubscribe(subscription: string) {
    this.send({
      type: 'unsubscribe',
      subscription,
    });
  }

  onMessage(handler: MessageHandler) {
    this.messageHandlers.add(handler);
    return () => {
      this.messageHandlers.delete(handler);
    };
  }

  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }
}

export const websocketService = new WebSocketService();
