import api from './api';

export const authService = {
  login: async (password: string) => {
    const response = await api.post('/auth/login', { password });
    const { access_token } = response.data;
    localStorage.setItem('token', access_token);
    return access_token;
  },

  logout: () => {
    localStorage.removeItem('token');
  },

  isAuthenticated: () => {
    return !!localStorage.getItem('token');
  },

  getToken: () => {
    return localStorage.getItem('token');
  },
};
