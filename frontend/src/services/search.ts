import { ProcessingOptions, SearchRequest, SearchResponse } from '../types';
import api from './api';

export const searchService = {
  search: async (request: SearchRequest) => {
    const response = await api.post<SearchResponse>('/search', request);
    return response.data;
  },

  extractClip: async (videoId: string, startSec: number, duration: number = 10) => {
    const response = await api.post('/search/clip', null, {
      params: { video_id: videoId, start_sec: startSec, duration },
    });
    return response.data;
  },

  analyzeClip: async (clipS3Key: string, videoId: string, startSec: number, duration: number) => {
    const response = await api.post('/search/analyze-clip', {
      clip_s3_key: clipS3Key,
      video_id: videoId,
      start_sec: startSec,
      duration: duration
    });
    return response.data;
  },

  processClip: async (
    videoId: string,
    segmentId: number,
    clipS3Key: string,
    options: ProcessingOptions
  ) => {
    const response = await api.post('/search/process-clip', options, {
      params: { video_id: videoId, segment_id: segmentId, clip_s3_key: clipS3Key },
    });
    return response.data;
  },

  rateResult: async (rating: {
    query: string;
    video_id: string;
    segment_id: number;
    rating: number;
    similarity_score: number;
    model_type: 'nova-premier' | 'marengo';
  }) => {
    const response = await api.post('/search/rate', rating);
    return response.data;
  },

  getPresets: async () => {
    const response = await api.get('/search/presets');
    return response.data;
  },
};
