import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export const api = axios.create({
  baseURL: `${API_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 120000, // 120 seconds for video processing
});

// Auto-login function
const autoLogin = async () => {
  try {
    const response = await axios.post(`${API_URL}/api/auth/login`, {
      password: 'minfy2025'
    });
    const token = response.data.access_token;
    localStorage.setItem('token', token);
    return token;
  } catch (error) {
    console.error('Auto-login failed:', error);
    return null;
  }
};

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    let token = localStorage.getItem('token');

    // If no token, try to auto-login
    if (!token) {
      token = await autoLogin();
    }

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access - try to re-authenticate
      console.log('Token expired, attempting re-authentication...');
      localStorage.removeItem('token');

      const newToken = await autoLogin();
      if (newToken) {
        // Retry the original request with new token
        error.config.headers.Authorization = `Bearer ${newToken}`;
        return api.request(error.config);
      }
    }
    return Promise.reject(error);
  }
);

export default api;
