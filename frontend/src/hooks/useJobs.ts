import { useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '../services/api';

interface Task {
  type: string;
  status: string;
  has_result?: boolean;
  arn?: string;
  task_id?: string;
  started_at?: string;
  completed_at?: string;
  duration_seconds?: number;
  chunk_count?: number;
  clip_count?: number;
  nova_completed?: string;
  marengo_completed?: string;
  progress?: {
    percent: number;
    status: string;
    current: number;
    total: number;
  };
}

interface VideoTask {
  video_id: string;
  video_status: string;
  filename: string;
  tasks: Array<Task>;
}

export const useVideoTasks = (limit: number = 10) => {
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ['videoTasks', limit],
    queryFn: async (): Promise<VideoTask[]> => {
      try {
        // Get recent videos
        const videosResponse = await api.get('/videos', { params: { limit } });
        const videos = Array.isArray(videosResponse.data) ? videosResponse.data : [];
        
        if (videos.length === 0) {
          return [];
        }
        
        // Get task status for each video
        const taskPromises = videos.map(async (video: any) => {
          try {
            const taskData = await api.get(`/tasks/video/${video.video_id}`);
            return taskData.data;
          } catch (e) {
            console.error('Error fetching tasks for video', video.video_id, ':', e);
            return null;
          }
        });
        
        const tasks = await Promise.all(taskPromises);
        return tasks.filter(Boolean);
      } catch (error) {
        console.error('Error fetching video tasks:', error);
        return [];
      }
    },
    staleTime: 5000, // Consider data fresh for 5 seconds (jobs change frequently)
    gcTime: 2 * 60 * 1000, // Keep in cache for 2 minutes
    refetchInterval: 5000, // Auto-refetch every 5 seconds
    refetchOnWindowFocus: true,
  });

  // Function to update a specific video task in the cache
  const updateVideoTaskInCache = (videoId: string, updates: Partial<VideoTask>) => {
    queryClient.setQueryData(['videoTasks', limit], (oldData: VideoTask[] | undefined) => {
      if (!oldData) return oldData;
      
      return oldData.map(videoTask => 
        videoTask.video_id === videoId 
          ? { ...videoTask, ...updates }
          : videoTask
      );
    });
  };

  // Function to update a specific task within a video
  const updateTaskInCache = (videoId: string, taskType: string, updates: Partial<Task>) => {
    queryClient.setQueryData(['videoTasks', limit], (oldData: VideoTask[] | undefined) => {
      if (!oldData) return oldData;
      
      return oldData.map(videoTask => {
        if (videoTask.video_id !== videoId) return videoTask;
        
        return {
          ...videoTask,
          tasks: videoTask.tasks.map(task =>
            task.type === taskType
              ? { ...task, ...updates }
              : task
          )
        };
      });
    });
  };

  // Function to invalidate and refetch tasks
  const refetchTasks = () => {
    queryClient.invalidateQueries({ queryKey: ['videoTasks'] });
  };

  return {
    ...query,
    updateVideoTaskInCache,
    updateTaskInCache,
    refetchTasks,
  };
};
