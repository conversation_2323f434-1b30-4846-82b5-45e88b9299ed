import { useQuery, useQueryClient } from '@tanstack/react-query';
import { videosService } from '../services/videos';
import { Video } from '../types/video';

interface UseVideosOptions {
  status_filter?: string;
  model_filter?: string;
  skip?: number;
  limit?: number;
}

export const useVideos = (options: UseVideosOptions = {}) => {
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ['videos', options],
    queryFn: () => videosService.listVideos(options),
    staleTime: 30000, // Consider data fresh for 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Don't refetch on mount if data exists
  });

  // Function to update a specific video in the cache
  const updateVideoInCache = (videoId: string, updates: Partial<Video>) => {
    queryClient.setQueryData(['videos', options], (oldData: Video[] | undefined) => {
      if (!oldData) return oldData;
      
      return oldData.map(video => 
        video.video_id === videoId 
          ? { ...video, ...updates }
          : video
      );
    });
  };

  // Function to add a new video to the cache
  const addVideoToCache = (newVideo: Video) => {
    queryClient.setQueryData(['videos', options], (oldData: Video[] | undefined) => {
      if (!oldData) return [newVideo];
      
      // Check if video already exists
      const exists = oldData.some(video => video.video_id === newVideo.video_id);
      if (exists) return oldData;
      
      // Add to beginning of list
      return [newVideo, ...oldData];
    });
  };

  // Function to remove a video from the cache
  const removeVideoFromCache = (videoId: string) => {
    queryClient.setQueryData(['videos', options], (oldData: Video[] | undefined) => {
      if (!oldData) return oldData;
      
      return oldData.filter(video => video.video_id !== videoId);
    });
  };

  // Function to invalidate and refetch videos
  const refetchVideos = () => {
    queryClient.invalidateQueries({ queryKey: ['videos'] });
  };

  return {
    ...query,
    updateVideoInCache,
    addVideoToCache,
    removeVideoFromCache,
    refetchVideos,
  };
};

// Hook for a single video
export const useVideo = (videoId: string) => {
  return useQuery({
    queryKey: ['video', videoId],
    queryFn: () => videosService.getVideo(videoId),
    staleTime: 60000, // Consider data fresh for 1 minute
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    enabled: !!videoId,
  });
};
