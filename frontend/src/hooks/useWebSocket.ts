import { useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { websocketService, WebSocketMessage } from '../services/websocket';
import { Video } from '../types/video';

export const useWebSocket = () => {
  const [isConnected, setIsConnected] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    console.log('🔌 Setting up WebSocket connection...');
    
    // Connect to WebSocket
    websocketService.connect();

    // Subscribe to messages
    const unsubscribe = websocketService.onMessage((message: WebSocketMessage) => {
      console.log('📨 WebSocket message received:', message);

      if (message.type === 'connection') {
        setIsConnected(true);
        console.log('✅ WebSocket connected');
      } 
      else if (message.type === 'video_update' && message.video_id) {
        console.log(`🔄 Updating video ${message.video_id} status to ${message.status}`);
        
        // Update all video queries in cache
        queryClient.setQueriesData(
          { queryKey: ['videos'] },
          (oldData: Video[] | undefined) => {
            if (!oldData) return oldData;
            
            return oldData.map(video =>
              video.video_id === message.video_id
                ? { ...video, status: (message.status || video.status) as Video['status'] }
                : video
            );
          }
        );

        // Also update single video query if it exists
        queryClient.setQueryData(
          ['video', message.video_id],
          (oldData: Video | undefined) => {
            if (!oldData) return oldData;
            return { ...oldData, status: (message.status || oldData.status) as Video['status'] };
          }
        );

        // Update video tasks cache
        queryClient.setQueriesData(
          { queryKey: ['videoTasks'] },
          (oldData: any[] | undefined) => {
            if (!oldData) return oldData;
            
            return oldData.map(videoTask =>
              videoTask.video_id === message.video_id
                ? { ...videoTask, video_status: message.status || videoTask.video_status }
                : videoTask
            );
          }
        );
      } 
      else if (message.type === 'job_update' && message.video_id) {
        console.log(`📊 Job update for video ${message.video_id}: ${message.progress}%`);
        
        // Update video tasks cache with job progress
        queryClient.setQueriesData(
          { queryKey: ['videoTasks'] },
          (oldData: any[] | undefined) => {
            if (!oldData) return oldData;
            
            return oldData.map(videoTask => {
              if (videoTask.video_id !== message.video_id) return videoTask;
              
              return {
                ...videoTask,
                tasks: videoTask.tasks.map((task: any) => {
                  // Update the relevant task based on job_id or type
                  if (message.job_id?.includes(task.type) || 
                      (message.job_id?.includes('emb_') && task.type.includes('embedding'))) {
                    return {
                      ...task,
                      status: message.status || task.status,
                      progress: message.progress ? {
                        percent: message.progress,
                        status: message.message || task.progress?.status || '',
                        current: task.progress?.current || 0,
                        total: task.progress?.total || 100
                      } : task.progress
                    };
                  }
                  return task;
                })
              };
            });
          }
        );
      }
      else if (message.type === 'upload_progress' && message.video_id) {
        console.log(`📤 Upload progress for video ${message.video_id}: ${message.progress}%`);
        
        // Update upload progress in video tasks
        queryClient.setQueriesData(
          { queryKey: ['videoTasks'] },
          (oldData: any[] | undefined) => {
            if (!oldData) return oldData;
            
            return oldData.map(videoTask => {
              if (videoTask.video_id !== message.video_id) return videoTask;
              
              return {
                ...videoTask,
                tasks: videoTask.tasks.map((task: any) => {
                  if (task.type === 'upload') {
                    return {
                      ...task,
                      progress: {
                        percent: message.progress || 0,
                        status: message.message || 'Uploading...',
                        current: message.progress || 0,
                        total: 100
                      }
                    };
                  }
                  return task;
                })
              };
            });
          }
        );
      }
    });

    // Cleanup on unmount
    return () => {
      console.log('🔌 Cleaning up WebSocket connection...');
      unsubscribe();
      websocketService.disconnect();
      setIsConnected(false);
    };
  }, [queryClient]);

  return {
    isConnected,
    send: websocketService.send.bind(websocketService),
    subscribe: websocketService.subscribe.bind(websocketService),
    unsubscribe: websocketService.unsubscribe.bind(websocketService),
  };
};
