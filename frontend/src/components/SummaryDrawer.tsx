import React from 'react';
import { X, FileText, AlertTriangle, CheckCircle } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

interface SummaryDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  summary: string;
  videoTitle?: string;
}

export const SummaryDrawer: React.FC<SummaryDrawerProps> = ({
  isOpen,
  onClose,
  summary,
  videoTitle,
}) => {
  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={onClose}
      />

      {/* Drawer */}
      <div
        className={`fixed right-0 top-0 h-full w-full md:w-2/3 lg:w-1/2 bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Header */}
        <div className="sticky top-0 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-4 flex items-center justify-between shadow-md">
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6" />
            <div>
              <h2 className="text-xl font-bold">Safety Report</h2>
              {videoTitle && (
                <p className="text-sm text-blue-100 mt-0.5">{videoTitle}</p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-blue-800 rounded-full transition-colors"
            aria-label="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="h-[calc(100%-144px)] overflow-y-auto">
          <div className="p-6 pb-24">
            {/* Markdown Content with Custom Styling */}
            <div className="prose prose-slate max-w-none">
              <ReactMarkdown
                components={{
                  h1: ({ node, ...props }) => (
                    <h1 className="text-2xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-blue-600" {...props} />
                  ),
                  h2: ({ node, ...props }) => (
                    <h2 className="text-xl font-semibold text-gray-800 mt-6 mb-3 flex items-center gap-2" {...props} />
                  ),
                  h3: ({ node, ...props }) => (
                    <h3 className="text-lg font-semibold text-gray-700 mt-4 mb-2" {...props} />
                  ),
                  p: ({ node, ...props }) => (
                    <p className="text-gray-600 leading-relaxed mb-3" {...props} />
                  ),
                  ul: ({ node, ...props }) => (
                    <ul className="list-none space-y-2 my-4" {...props} />
                  ),
                  li: ({ node, children, ...props }) => {
                    const text = String(children);
                    const isRisk = text.includes('⚠️') || text.includes('Risk') || text.includes('CRITICAL');
                    const isSuccess = text.includes('✅') || text.includes('Safe') || text.includes('No');
                    
                    return (
                      <li className="flex items-start gap-2" {...props}>
                        {isRisk ? (
                          <AlertTriangle className="w-4 h-4 text-red-500 mt-1 flex-shrink-0" />
                        ) : isSuccess ? (
                          <CheckCircle className="w-4 h-4 text-green-500 mt-1 flex-shrink-0" />
                        ) : (
                          <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        )}
                        <span className={`${isRisk ? 'text-red-700 font-medium' : isSuccess ? 'text-green-700' : 'text-gray-600'}`}>
                          {children}
                        </span>
                      </li>
                    );
                  },
                  strong: ({ node, ...props }) => (
                    <strong className="font-semibold text-gray-900" {...props} />
                  ),
                  hr: ({ node, ...props }) => (
                    <hr className="my-6 border-gray-200" {...props} />
                  ),
                  blockquote: ({ node, ...props }) => (
                    <blockquote className="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 rounded-r" {...props} />
                  ),
                  code: ({ node, inline, ...props }: any) =>
                    inline ? (
                      <code className="bg-gray-100 text-red-600 px-1.5 py-0.5 rounded text-sm font-mono" {...props} />
                    ) : (
                      <code className="block bg-gray-900 text-gray-100 p-4 rounded-lg my-4 overflow-x-auto text-sm font-mono" {...props} />
                    ),
                }}
              >
                {summary}
              </ReactMarkdown>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 bg-gray-50 px-6 py-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors"
          >
            Close Report
          </button>
        </div>
      </div>
    </>
  );
};
