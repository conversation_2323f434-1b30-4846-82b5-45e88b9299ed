import { create } from 'zustand';
import { authService } from '../services/auth';

interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  login: (password: string) => Promise<void>;
  logout: () => void;
  checkAuth: () => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  isAuthenticated: authService.isAuthenticated(),
  token: authService.getToken(),
  
  login: async (password: string) => {
    const token = await authService.login(password);
    set({ isAuthenticated: true, token });
  },
  
  logout: () => {
    authService.logout();
    set({ isAuthenticated: false, token: null });
  },
  
  checkAuth: () => {
    const isAuthenticated = authService.isAuthenticated();
    const token = authService.getToken();
    set({ isAuthenticated, token });
  },
}));
