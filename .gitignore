# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Node.js / Frontend
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp
.pnp.js
package-lock.json
yarn.lock

# Frontend build
frontend/build/
frontend/dist/
frontend/.next/
frontend/out/

# Testing
coverage/
.nyc_output/
*.coverage
.pytest_cache/

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Database
chroma_db/
*.db
*.sqlite3

# Temporary files
*.tmp
*.temp
/tmp/

# AWS credentials (keep .pem files but ignore other sensitive files)
.env
*.env
aws/output.json
!aws/*.pem

# Backup files
*_backup.py
*.bak
app_older.py

# Lambda deployment packages
*.zip
lambda_package/
lambda_video_processor.zip

# Video files (too large for git)
assets/
*.mp4
*.avi
*.mov
*.mkv

# Cookies and sensitive data
cookies.txt

# Cache files
search_cache.pkl

# Documentation (generated)
/docs/
docs_archive/

# Docker volumes
postgres_data/
weaviate_data/
redis_data/

# Alembic
alembic/versions/*.pyc

# YOLOv8 models (large files)
*.pt
!yolov8n.pt

# Jupyter notebooks
.ipynb_checkpoints/
*.ipynb

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local
