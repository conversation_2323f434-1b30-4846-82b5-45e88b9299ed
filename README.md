# 🎥 Samsara Video Analysis Platform

**Clean FastAPI + React Architecture for Video Search & Safety Analysis**

---

## 🏗️ Current Architecture

This repository contains a **clean, production-ready FastAPI + React stack** with:

- **Backend:** FastAPI with PostgreSQL, Weaviate, Redis, Celery
- **Frontend:** React 18 + TypeScript with TailwindCSS
- **AI/ML:** YOLOv8 object detection, AWS Bedrock integration
- **Infrastructure:** Docker Compose orchestration

### Repository Structure
```
samsara-poc-main/
├── README.md              # This documentation
├── docker-compose.yml     # Full stack orchestration
├── aws/                   # AWS credentials
│   └── samsara-poc-video.pem
├── backend/               # FastAPI application
│   ├── app/               # FastAPI code
│   ├── logs/              # Application logs
│   ├── requirements.txt   # Python dependencies
│   └── yolov8n.pt        # YOLO model
└── frontend/              # React application
    ├── src/               # React TypeScript code
    ├── package.json       # Node dependencies
    └── ...
```

---

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- AWS Account with Bedrock access (Nova Premier + Marengo)
- 8GB+ RAM recommended

### Start the System
```bash
cd samsara-poc-main
docker compose up -d
```

### Access
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs

### Default Credentials
- **Username:** `admin`
- **Password:** `minfy2025`

---

## 🏗️ Technology Stack

### Frontend
- **React 18** + TypeScript
- **TailwindCSS** for styling
- **React Query** for data fetching
- **Lucide React** for icons
- **Vite** for build tooling

### Backend
- **FastAPI** (Python) - Modern async web framework
- **PostgreSQL** - Relational database
- **Weaviate** - Vector database for embeddings
- **Redis** - Caching and message broker
- **Celery** - Background task processing
- **SQLAlchemy** - ORM with Alembic migrations

### AI/ML Services
- **YOLOv8** - Object detection and tracking
- **MediaPipe** - Face detection for PII blurring
- **AWS Bedrock** - Nova Premier & Marengo embeddings
- **OpenCV** - Video processing
- **scikit-learn** - ML utilities

### Infrastructure
- **Docker Compose** - Container orchestration
- **Nginx** (production) - Reverse proxy
- **AWS S3** - Video storage
- **AWS Rekognition** - Additional AI services

---

## 🔧 Configuration

### Environment Variables

Create `.env` file in the root directory:

```bash
# AWS Credentials
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=your_account_id

# S3 Configuration
S3_BUCKET=your-bucket-name

# Database
POSTGRES_USER=samsara
POSTGRES_PASSWORD=samsara_dev_pass
POSTGRES_DB=samsara_db

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Weaviate
WEAVIATE_URL=http://weaviate:8080

# Application
DEBUG=true
SECRET_KEY=your-secret-key
ADMIN_PASSWORD=minfy2025
```

---

## 🚀 Development Guide

### Local Development Setup

**1. Clone and Setup**
```bash
git clone <repository-url>
cd samsara-poc-main
cp .env.example .env  # Configure your environment
```

**2. Start with Docker (Recommended)**
```bash
docker compose up -d
```

**3. Or Run Services Individually**

**Backend:**
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Frontend:**
```bash
cd frontend
npm install
npm start
```

**Celery Worker:**
```bash
cd backend
celery -A app.core.celery_app worker --loglevel=info
```

### Available Services
- **Backend API:** http://localhost:8000
- **Frontend:** http://localhost:3000
- **API Docs:** http://localhost:8000/docs
- **PostgreSQL:** localhost:5432
- **Redis:** localhost:6379
- **Weaviate:** http://localhost:8080

---

## 🛠️ Backend Services Detail

### FastAPI Application Structure

```
backend/app/
├── api/routes/          # API endpoints
│   ├── auth.py         # Authentication
│   ├── videos.py       # Video management
│   ├── search.py       # Search functionality
│   ├── jobs.py         # Job monitoring
│   └── websocket.py    # Real-time updates
├── core/               # Core configuration
│   ├── config.py       # Settings
│   ├── database.py     # Database setup
│   └── celery_app.py   # Task queue
├── models/             # Database models
│   ├── video.py        # Video metadata
│   └── job.py          # Processing jobs
├── services/           # Business logic
│   ├── aws_service.py  # AWS integration
│   ├── yolo_service.py # Object detection
│   ├── video_service.py # Video processing
│   └── vector_service.py # Embeddings
└── tasks/              # Background tasks
    ├── video_tasks.py  # Video processing
    ├── embedding_tasks.py # AI embeddings
    └── summary_tasks.py # Report generation
```

### Key Features Available

✅ **Video Processing Pipeline**
- Upload handling (direct + YouTube)
- YOLOv8 object detection with tracking
- PII blurring (faces, license plates)
- AWS Bedrock embeddings (Nova + Marengo)
- Automated safety report generation

✅ **Search & Analytics**
- Natural language video search
- Vector similarity matching
- Real-time progress tracking via WebSocket
- Job monitoring and status updates

✅ **Database & Storage**
- PostgreSQL for metadata
- Weaviate for vector embeddings
- Redis for caching and task queue
- AWS S3 for video storage

---

## 🔍 Troubleshooting

### Check Logs
```bash
# Backend API logs
docker logs samsara-backend --tail 50

# Celery worker logs
docker logs samsara-celery-worker --tail 50

# Frontend logs
docker logs samsara-frontend --tail 50

# Application logs (inside backend container)
docker exec samsara-backend tail -f logs/app.log
```

### Restart Services
```bash
# Restart all services
docker compose restart

# Restart specific services
docker compose restart backend celery-worker frontend
```

### Database Management
```bash
# Reset PostgreSQL
docker compose down
docker volume rm samsara-poc-main_postgres_data
docker compose up -d

# Reset Weaviate
docker volume rm samsara-poc-main_weaviate_data

# Reset Redis
docker volume rm samsara-poc-main_redis_data
```

---

## 🚀 Next Steps

### For New Application Development

1. **Review Current Services**
   - Explore FastAPI endpoints at http://localhost:8000/docs
   - Check available Celery tasks in `backend/app/tasks/`
   - Review YOLO and video processing services

2. **Customize for Your Needs**
   - Modify API endpoints in `backend/app/api/routes/`
   - Update React components in `frontend/src/`
   - Add new background tasks as needed

3. **Deploy to Production**
   - Configure production environment variables
   - Set up proper SSL certificates
   - Configure monitoring and logging
   - Set up backup strategies

### Ready-to-Use Components

✅ **Authentication System** - JWT-based auth with admin controls
✅ **File Upload Service** - Direct upload + YouTube URL support
✅ **Video Processing** - YOLOv8 object detection with PII blurring
✅ **AI Integration** - AWS Bedrock embeddings and summaries
✅ **Real-time Updates** - WebSocket for progress tracking
✅ **Database Models** - PostgreSQL with Alembic migrations
✅ **Vector Search** - Weaviate integration for embeddings
✅ **Background Tasks** - Celery with Redis broker

---

## 📄 License

Proprietary - Samsara Internal Use

---

## 📞 Support

For development questions:
1. Check API documentation: http://localhost:8000/docs
2. Review backend logs: `docker logs samsara-backend`
3. Inspect database: Connect to PostgreSQL on localhost:5432

---

**🎯 Repository Status: Clean & Ready for Development**
